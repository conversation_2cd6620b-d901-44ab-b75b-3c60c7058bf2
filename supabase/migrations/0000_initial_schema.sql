/*
================================================================================
-- File: farms_schema_database_setup.sql
--
-- Version: 3.0 - Complete and Enhanced Schema
--
-- Description:
-- This script defines the complete, production-ready, and hierarchically
-- structured database schema for the NewTerra onboarding wizard. This enhanced
-- schema addresses all business requirements and organizes data under four
-- parent "step" tables, directly mirroring the frontend user journey.
--
-- Architectural Principles:
-- 1. 4-Step Hierarchical Structure: All data organized under four parent tables
--    corresponding to the major steps in the onboarding flow
-- 2. Session-Centric Root: All data cascades from 'onboarding_sessions'
-- 3. Complete Requirements Coverage: Every data attribute from business
--    requirements is properly captured and validated
-- 4. Data Validation at Core: CHECK constraints enforce formats and business rules
-- 5. Explicit Encryption Requirement: Sensitive data stored as BYTEA with
--    encryption via Edge Functions
-- 6. Performance Optimization: Strategic indexing on foreign keys and query paths
================================================================================
*/

-- Clean slate approach for refactoring
DROP SCHEMA IF EXISTS farms CASCADE;

--==============================================================================
-- 0. SCHEMA SETUP AND FOUNDATIONAL TYPES
--==============================================================================

CREATE SCHEMA IF NOT EXISTS farms;
COMMENT ON SCHEMA farms IS 'Complete schema for NewTerra farm onboarding and management process covering all business requirements.';

-- Business-specific enums with comprehensive coverage
CREATE TYPE farms.activity_type_enum AS ENUM (
    'Cropping', 'Livestock', 'Mixed Farming', 'Horticulture', 
    'Dairy', 'Aquaculture', 'Forestry', 'Other'
);

CREATE TYPE farms.agreement_type_enum AS ENUM (
    'Service Agreement', 'Privacy Policy', 'Direct Debit', 'Terms and Conditions'
);

-- Updated enum for contact authority levels
CREATE TYPE farms.authority_level_enum AS ENUM (
    'Full Decision Making Authority', 'Operational Decisions Only', 'Information Contact Only'
);

-- Updated enum for contact types with required business roles
CREATE TYPE farms.contact_type_enum AS ENUM (
    'Primary Contact', 'Owner/Director', 'General Manager', 'Operations Manager', 
    'Financial Controller', 'Accountant', 'BAS Agent', 'Key Staff',
    'Admin', 'Accounts', 'Personal', 'Emergency Contact',
    'Legal Representative', 'Insurance Contact', 'Supplier Contact', 'Other'
);

-- Australian business structure types for validation
CREATE TYPE farms.business_structure_enum AS ENUM (
    'Sole Trader', 'Company', 'Partnership', 'Trust', 
    'Cooperative', 'Other'
);

-- License types with specific water license support
CREATE TYPE farms.license_type_enum AS ENUM (
    'Chemical Permit', 'Machinery License', 'Food Safety Cert', 
    'Water License', 'Heavy Vehicle License', 'Quad Bike License', 'Other'
);

-- Asset categories for proper differentiation
CREATE TYPE farms.asset_category_enum AS ENUM (
    'Vehicle', 'Equipment', 'Insurance'
);

-- Communication preferences for structured selection
CREATE TYPE farms.communication_method_enum AS ENUM (
    'Email', 'Phone', 'SMS', 'WhatsApp'
);

CREATE TYPE farms.reporting_frequency_enum AS ENUM (
    'Weekly', 'Fortnightly', 'Monthly'
);

CREATE TYPE farms.bas_frequency_enum AS ENUM (
    'Monthly', 'Quarterly', 'Annually'
);

-- Enhanced enum types for financial systems configuration
CREATE TYPE farms.chart_of_accounts_setup_enum AS ENUM (
    'Default Template', 'Custom Setup', 'Imported from Previous System', 'Accountant Configured'
);

CREATE TYPE farms.bas_preparation_enum AS ENUM (
    'Self-Prepared', 'Accountant Prepared', 'BAS Agent', 'Bookkeeper'
);

-- New enum types for enhanced schema
CREATE TYPE farms.employment_type_enum AS ENUM (
    'Full-time', 'Part-time', 'Casual', 'Contract', 'Seasonal'
);

CREATE TYPE farms.supplier_type_enum AS ENUM (
    'Chemical Supplier', 'Equipment Rental', 'Feed Supplier', 'Seed Supplier',
    'Fertilizer Supplier', 'Veterinary Services', 'Transport Services',
    'Maintenance Services', 'Other'
);

CREATE TYPE farms.accounting_method_enum AS ENUM (
    'Cash Accounting', 'Accrual Accounting'
);

CREATE TYPE farms.license_status_enum AS ENUM (
    'Current', 'Pending Renewal', 'Expired', 'Under Review'
);

CREATE TYPE farms.renewal_frequency_enum AS ENUM (
    'Annual', 'Biennial', 'Every 3 Years', 'Every 5 Years', 'One-time only'
);

CREATE TYPE farms.insurance_type_enum AS ENUM (
    'Public Liability', 'Product Liability', 'Professional Indemnity',
    'Workers Compensation', 'Motor Vehicle', 'Property', 'Crop Insurance',
    'Livestock Insurance', 'Business Interruption', 'Cyber Liability', 'Other'
);

CREATE TYPE farms.vehicle_type_enum AS ENUM (
    'Tractor', 'Harvester', 'Truck', 'Utility Vehicle', 'Trailer',
    'Motorbike', 'Quad Bike', 'Other Farm Vehicle'
);

-- Utility function for automatic timestamp updates
CREATE OR REPLACE FUNCTION public.trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
COMMENT ON FUNCTION public.trigger_set_timestamp IS 'Automatically updates the updated_at timestamp on row modification.';

--==============================================================================
-- 1. ROOT SESSION TABLE
--==============================================================================

CREATE TABLE farms.onboarding_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'in_progress', 
    current_step INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_status CHECK (status IN ('in_progress', 'completed', 'archived')),
    CONSTRAINT chk_current_step CHECK (current_step BETWEEN 1 AND 5)
);

COMMENT ON TABLE farms.onboarding_sessions IS 'Root table for user onboarding journey. All data cascades from here via user_id for RLS security.';
COMMENT ON COLUMN farms.onboarding_sessions.status IS 'Tracks onboarding progress: in_progress, completed, archived';
COMMENT ON COLUMN farms.onboarding_sessions.current_step IS 'Current step (1-5) for UI state management, 5 = completed';

CREATE INDEX idx_onboarding_sessions_user_id ON farms.onboarding_sessions(user_id);
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.onboarding_sessions FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

--==============================================================================
-- 2. PARENT STEP TABLES
-- These represent the five major sections of the onboarding wizard
--==============================================================================

CREATE TABLE farms.step_1_business_profile (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL UNIQUE REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE farms.step_1_business_profile IS 'Parent table for Step 1: Business registration, contacts, and addresses';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.step_1_business_profile FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.step_2_farm_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL UNIQUE REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE farms.step_2_farm_operations IS 'Parent table for Step 2: Farm activities, licenses, suppliers, and compliance';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.step_2_farm_operations FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.step_3_financial_systems (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL UNIQUE REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE farms.step_3_financial_systems IS 'Parent table for Step 3: Bookkeeping, payroll, and asset management';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.step_3_financial_systems FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.step_4_registrations_insurance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL UNIQUE REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE farms.step_4_registrations_insurance IS 'Parent table for Step 4: Vehicle registrations, licenses, and insurance policies';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.step_4_registrations_insurance FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.step_5_finalization (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL UNIQUE REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
COMMENT ON TABLE farms.step_5_finalization IS 'Parent table for Step 5: Legal agreements, data migration, and communication preferences';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.step_5_finalization FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

-- NOTE: This table is DEPRECATED and will be removed in a future migration
-- All functionality has been migrated to step_5_finalization
CREATE TABLE farms.step_4_agreements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    onboarding_session_id UUID NOT NULL UNIQUE REFERENCES farms.onboarding_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    -- Mark as deprecated in constraint
    CONSTRAINT deprecated_table_marker CHECK (true) -- This table is deprecated
);
COMMENT ON TABLE farms.step_4_agreements IS 'DEPRECATED: This table will be removed in Phase 6. All functionality migrated to step_5_finalization';

--==============================================================================
-- 3. STEP 1 CHILD TABLES: BUSINESS PROFILE & CONTACT DETAILS
--==============================================================================

CREATE TABLE farms.business_registration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_1_id UUID NOT NULL UNIQUE REFERENCES farms.step_1_business_profile(id) ON DELETE CASCADE,
    
    -- Core business identification (all required by Australian law)
    full_business_name TEXT NOT NULL,
    trading_name TEXT,
    abn TEXT NOT NULL,
    acn TEXT,
    is_gst_registered BOOLEAN NOT NULL DEFAULT false,
    business_structure farms.business_structure_enum NOT NULL,
    
    -- Primary business contact (separate from individual contacts)
    primary_business_phone TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    -- Australian format validation
    CONSTRAINT chk_abn_format CHECK (abn ~ '^[0-9]{2}\s[0-9]{3}\s[0-9]{3}\s[0-9]{3}$'),
    CONSTRAINT chk_acn_format CHECK (acn IS NULL OR acn ~ '^[0-9]{3}\s[0-9]{3}\s[0-9]{3}$'),
    CONSTRAINT chk_primary_phone_format CHECK (primary_business_phone IS NULL OR primary_business_phone ~ '^\\+61\\d{9}$')
);

COMMENT ON TABLE farms.business_registration IS 'Core legal business information as required by Australian business registration';
COMMENT ON COLUMN farms.business_registration.abn IS 'Australian Business Number in format: XX XXX XXX XXX';
COMMENT ON COLUMN farms.business_registration.acn IS 'Australian Company Number in format: XXX XXX XXX (companies only)';
COMMENT ON COLUMN farms.business_registration.primary_business_phone IS 'Main business phone in international format +61XXXXXXXXX';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.business_registration FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_1_id UUID NOT NULL REFERENCES farms.step_1_business_profile(id) ON DELETE CASCADE,
    
    address_type TEXT NOT NULL,
    full_address_text TEXT NOT NULL,
    
    -- Structured address components for search/validation
    street TEXT,
    locality TEXT, -- Suburb or city
    state TEXT,
    postcode TEXT,
    country TEXT DEFAULT 'Australia',
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_address_type CHECK (address_type IN (
        'Business Address', 'Property Address', 'Postal Address'
    ))
);

COMMENT ON TABLE farms.addresses IS 'Business postal and property addresses with structured components';
COMMENT ON COLUMN farms.addresses.address_type IS 'Address purpose: Postal, Property, Billing, Shipping, Emergency Contact, or Other';
COMMENT ON COLUMN farms.addresses.full_address_text IS 'Complete address as single string for display purposes';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.addresses FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_addresses_step_1_id ON farms.addresses(step_1_id);

CREATE TABLE farms.contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_1_id UUID NOT NULL REFERENCES farms.step_1_business_profile(id) ON DELETE CASCADE,
    
    contact_type farms.contact_type_enum NOT NULL,
    name TEXT NOT NULL,
    title_or_firm TEXT,
    title_position TEXT, -- Enhanced: Specific job title/position
    authority_level farms.authority_level_enum, -- Enhanced: Decision-making authority
    email TEXT,
    phone TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_email_format CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'),
    CONSTRAINT chk_phone_format CHECK (phone IS NULL OR phone ~ '^\\+61\\d{9}$')
);

COMMENT ON TABLE farms.contacts IS 'All human contacts including main contact, accountant, BAS agent, and key staff';
COMMENT ON COLUMN farms.contacts.contact_type IS 'Categorizes contact role - Main Contact is required, others optional';
COMMENT ON COLUMN farms.contacts.title_or_firm IS 'Job title for individuals or firm name for external professionals';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.contacts FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_contacts_step_1_id ON farms.contacts(step_1_id);
CREATE INDEX idx_contacts_type ON farms.contacts(contact_type);

-- Add this after the contacts table creation
CREATE TABLE farms.key_staff (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_1_id UUID NOT NULL REFERENCES farms.step_1_business_profile(id) ON DELETE CASCADE,
    
    staff_name TEXT NOT NULL,
    role_or_title TEXT,
    department_area TEXT, -- Enhanced: Department or operational area
    employment_type farms.employment_type_enum, -- Enhanced: Employment classification
    supervisor_id UUID REFERENCES farms.contacts(id), -- Fixed: References contacts table
    contact_email TEXT,
    contact_phone TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_staff_email_format CHECK (contact_email IS NULL OR contact_email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'),
    CONSTRAINT chk_staff_phone_format CHECK (contact_phone IS NULL OR contact_phone ~ '^\\+61\\d{9}$')
);

COMMENT ON TABLE farms.key_staff IS 'Key farm staff members and their contact details';
COMMENT ON COLUMN farms.key_staff.role_or_title IS 'Job title or role description (e.g., Farm Manager, Head Groundskeeper)';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.key_staff FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_key_staff_step_1_id ON farms.key_staff(step_1_id);

--==============================================================================
-- 4. STEP 2 CHILD TABLES: FARM OPERATIONS & COMPLIANCE
--==============================================================================

CREATE TABLE farms.activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,
    
    activity_type farms.activity_type_enum NOT NULL,
    
    -- Crop-specific details
    crop_type TEXT,
    crop_varieties TEXT[], -- Array to store multiple varieties (e.g., Valencia, Navel oranges)
    
    -- Livestock-specific details  
    livestock_type TEXT,
    approximate_numbers INTEGER,
    
    -- Enhanced production category fields
    production_method TEXT, -- e.g., 'Organic', 'Conventional', 'Biodynamic', 'Regenerative'
    certification_status TEXT, -- e.g., 'Certified Organic', 'In Conversion', 'Conventional'
    seasonal_pattern TEXT, -- e.g., 'Year Round', 'Seasonal', 'Rotation Based'
    primary_market TEXT, -- e.g., 'Domestic', 'Export', 'Direct Sales', 'Wholesale'
    
    -- Additional activity details
    activity_description TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_positive_numbers CHECK (approximate_numbers IS NULL OR approximate_numbers > 0),
    CONSTRAINT chk_production_method CHECK (production_method IS NULL OR production_method IN (
        'Organic', 'Conventional', 'Biodynamic', 'Regenerative', 'Integrated Pest Management', 'Other'
    )),
    CONSTRAINT chk_certification_status CHECK (certification_status IS NULL OR certification_status IN (
        'Certified Organic', 'In Conversion', 'Conventional', 'Other Certification'
    )),
    CONSTRAINT chk_seasonal_pattern CHECK (seasonal_pattern IS NULL OR seasonal_pattern IN (
        'Year Round', 'Seasonal', 'Rotation Based', 'Campaign Based'
    )),
    CONSTRAINT chk_primary_market CHECK (primary_market IS NULL OR primary_market IN (
        'Domestic', 'Export', 'Direct Sales', 'Wholesale', 'Processing', 'Mixed'
    ))
);

COMMENT ON TABLE farms.activities IS 'Detailed farming activities including crops, livestock, and other operations';
COMMENT ON COLUMN farms.activities.crop_varieties IS 'Array of crop varieties for detailed tracking (e.g., [Valencia, Navel] for oranges)';
COMMENT ON COLUMN farms.activities.approximate_numbers IS 'For livestock - approximate head count';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.activities FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_activities_step_2_id ON farms.activities(step_2_id);

CREATE TABLE farms.farm_info (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL UNIQUE REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,

    total_farm_size_hectares DECIMAL(10,2),
    total_farm_size_acres DECIMAL(10,2),
    farm_established_year INTEGER,
    primary_land_use TEXT,
    soil_types TEXT[],
    water_sources TEXT[],
    climate_zone TEXT,
    average_annual_rainfall_mm INTEGER,

    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

    CONSTRAINT chk_positive_farm_size CHECK (
        (total_farm_size_hectares IS NULL OR total_farm_size_hectares > 0) AND
        (total_farm_size_acres IS NULL OR total_farm_size_acres > 0)
    ),
    CONSTRAINT chk_established_year CHECK (
        farm_established_year IS NULL OR
        (farm_established_year >= 1800 AND farm_established_year <= EXTRACT(YEAR FROM CURRENT_DATE))
    ),
    CONSTRAINT chk_positive_rainfall CHECK (
        average_annual_rainfall_mm IS NULL OR average_annual_rainfall_mm >= 0
    )
);

COMMENT ON TABLE farms.farm_info IS 'Overall farm metadata including size, establishment details, and environmental characteristics';
COMMENT ON COLUMN farms.farm_info.soil_types IS 'Array of soil types present on the farm (e.g., [Clay, Sandy Loam, Black Soil])';
COMMENT ON COLUMN farms.farm_info.water_sources IS 'Array of water sources (e.g., [Bore Water, Dam, River, Mains Water])';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.farm_info FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.farm_blocks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,

    block_name TEXT NOT NULL,
    block_size_hectares DECIMAL(8,2),
    block_size_acres DECIMAL(8,2),
    primary_use TEXT NOT NULL,
    soil_type TEXT,
    irrigation_type TEXT,
    drainage_quality TEXT,
    slope_percentage DECIMAL(5,2),

    -- Location details
    gps_coordinates TEXT,
    boundary_description TEXT,

    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

    CONSTRAINT chk_positive_block_size CHECK (
        (block_size_hectares IS NULL OR block_size_hectares > 0) AND
        (block_size_acres IS NULL OR block_size_acres > 0)
    ),
    CONSTRAINT chk_slope_percentage CHECK (
        slope_percentage IS NULL OR (slope_percentage >= 0 AND slope_percentage <= 100)
    ),
    CONSTRAINT chk_primary_use CHECK (primary_use IN (
        'Cropping', 'Grazing', 'Horticulture', 'Dairy', 'Infrastructure',
        'Conservation', 'Water Storage', 'Other'
    )),
    CONSTRAINT chk_irrigation_type CHECK (irrigation_type IS NULL OR irrigation_type IN (
        'Drip', 'Sprinkler', 'Flood', 'Centre Pivot', 'Lateral Move', 'None', 'Other'
    )),
    CONSTRAINT chk_drainage_quality CHECK (drainage_quality IS NULL OR drainage_quality IN (
        'Excellent', 'Good', 'Fair', 'Poor', 'Very Poor'
    ))
);

COMMENT ON TABLE farms.farm_blocks IS 'Individual farm blocks/paddocks with detailed characteristics for precision management';
COMMENT ON COLUMN farms.farm_blocks.gps_coordinates IS 'GPS coordinates in decimal degrees format (e.g., -27.4698, 153.0251)';
COMMENT ON COLUMN farms.farm_blocks.slope_percentage IS 'Average slope percentage for the block (0-100%)';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.farm_blocks FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_farm_blocks_step_2_id ON farms.farm_blocks(step_2_id);
CREATE INDEX idx_farm_blocks_primary_use ON farms.farm_blocks(primary_use);

-- DEPRECATED: Licenses table migrated to Step 4 as licenses_new with enhanced fields
-- CREATE TABLE farms.licenses (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,
--
--     license_type farms.license_type_enum NOT NULL,
--     license_number TEXT,
--     issuing_authority TEXT,
--     issue_date DATE,
--     expiry_date DATE,
--
--     created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
--     updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
--
--     CONSTRAINT chk_expiry_date CHECK (expiry_date IS NULL OR issue_date IS NULL OR expiry_date > issue_date)
-- );
--
-- COMMENT ON TABLE farms.licenses IS 'All operational licenses including water licenses, chemical permits, and machinery licenses';
-- COMMENT ON COLUMN farms.licenses.license_type IS 'Specific license categories with Water License as distinct type';
-- COMMENT ON COLUMN farms.licenses.issuing_authority IS 'Government body or agency that issued the license';
--
-- CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.licenses FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
-- CREATE INDEX idx_licenses_step_2_id ON farms.licenses(step_2_id);
-- CREATE INDEX idx_licenses_expiry ON farms.licenses(expiry_date) WHERE expiry_date IS NOT NULL;

CREATE TABLE farms.suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,
    
    supplier_name TEXT NOT NULL,
    supplier_type farms.supplier_type_enum, -- Enhanced: Standardized supplier classification
    contact_person TEXT,
    contact_details TEXT,
    address TEXT, -- Enhanced: Supplier address
    phone TEXT, -- Enhanced: Supplier phone number
    email TEXT, -- Enhanced: Supplier email
    services_provided TEXT NOT NULL,
    supplier_category TEXT, -- e.g., 'Chemical Supplier', 'Equipment Rental', 'Feed Supplier'
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

    CONSTRAINT chk_supplier_email_format CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'),
    CONSTRAINT chk_supplier_phone_format CHECK (phone IS NULL OR phone ~ '^\\+61\\d{9}$')
);

COMMENT ON TABLE farms.suppliers IS 'All farm operation suppliers with categorization for better organization';
COMMENT ON COLUMN farms.suppliers.supplier_category IS 'Groups suppliers by type of service for reporting and management';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.suppliers FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_suppliers_step_2_id ON farms.suppliers(step_2_id);
CREATE INDEX idx_suppliers_category ON farms.suppliers(supplier_category);

CREATE TABLE farms.contracts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,
    supplier_id UUID REFERENCES farms.suppliers(id) ON DELETE SET NULL,
    
    contract_description TEXT NOT NULL,
    contract_type TEXT, -- e.g., 'Land Lease', 'Equipment Lease', 'Agistment', 'Sales Contract'
    contract_value DECIMAL(10,2),
    payment_terms TEXT, -- Enhanced: Payment terms and conditions
    contracting_party TEXT, -- Enhanced: Name of the other party to the contract
    start_date DATE,
    expiry_date DATE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_contract_dates CHECK (expiry_date IS NULL OR start_date IS NULL OR expiry_date > start_date),
    CONSTRAINT chk_contract_value CHECK (contract_value IS NULL OR contract_value >= 0)
);

COMMENT ON TABLE farms.contracts IS 'Key contracts with expiry tracking for proactive management';
COMMENT ON COLUMN farms.contracts.contract_type IS 'Categorizes contract for expiry monitoring (Land/Equipment leases, Agistment, Sales)';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.contracts FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_contracts_step_2_id ON farms.contracts(step_2_id);
CREATE INDEX idx_contracts_supplier_id ON farms.contracts(supplier_id);
CREATE INDEX idx_contracts_expiry ON farms.contracts(expiry_date) WHERE expiry_date IS NOT NULL;

CREATE TABLE farms.chemical_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_2_id UUID NOT NULL REFERENCES farms.step_2_farm_operations(id) ON DELETE CASCADE,
    
    product_name TEXT NOT NULL,
    manufacturer TEXT,
    active_ingredient TEXT, -- Enhanced: Primary active ingredient
    usage_purpose TEXT NOT NULL, -- e.g., 'Pesticide', 'Herbicide', 'Fertiliser', 'Fungicide'
    application_rate TEXT, -- e.g., '10L/ha', '5kg per 100L water'
    application_method TEXT, -- e.g., 'Spray', 'Granular', 'Injection'
    target_block_area DECIMAL(8,2), -- Enhanced: Specific area treated (hectares)
    weather_conditions TEXT, -- Enhanced: Weather conditions during application
    last_application_date DATE,
    withholding_period_days INTEGER,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_positive_withholding CHECK (withholding_period_days IS NULL OR withholding_period_days >= 0),
    CONSTRAINT chk_positive_target_area CHECK (target_block_area IS NULL OR target_block_area > 0)
);

COMMENT ON TABLE farms.chemical_usage IS 'Detailed chemical and fertilizer usage for compliance and safety tracking';
COMMENT ON COLUMN farms.chemical_usage.withholding_period_days IS 'Days between application and safe harvest/consumption';
COMMENT ON COLUMN farms.chemical_usage.application_rate IS 'Rate of application (e.g., L/ha, kg/100L)';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.chemical_usage FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_chemical_usage_step_2_id ON farms.chemical_usage(step_2_id);

--==============================================================================
-- 5. STEP 3 CHILD TABLES: FINANCIAL SYSTEMS & ASSET MANAGEMENT
--==============================================================================

CREATE TABLE farms.bookkeeping (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_3_id UUID NOT NULL UNIQUE REFERENCES farms.step_3_financial_systems(id) ON DELETE CASCADE,
    
    current_software TEXT NOT NULL,
    software_version TEXT,
    access_credentials BYTEA, -- Encrypted storage
    has_bank_feeds_enabled BOOLEAN NOT NULL DEFAULT false,
    bas_lodgement_frequency farms.bas_frequency_enum NOT NULL,
    
    -- Enhanced fields for Step 3 requirements
    accounting_method farms.accounting_method_enum NOT NULL,
    financial_year_end_month INTEGER NOT NULL,
    chart_of_accounts_setup farms.chart_of_accounts_setup_enum NOT NULL,
    bas_preparation farms.bas_preparation_enum NOT NULL,
    
    -- Accountant integration details
    accountant_has_access BOOLEAN NOT NULL DEFAULT false,
    accountant_access_level TEXT, -- e.g., 'View Only', 'Full Access', 'Advisor'
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    -- Validation constraints
    CONSTRAINT chk_financial_year_end_month CHECK (
        financial_year_end_month >= 1 AND financial_year_end_month <= 12
    )
);

COMMENT ON TABLE farms.bookkeeping IS 'Enhanced financial system details with accounting method, year-end, and chart of accounts configuration';
COMMENT ON COLUMN farms.bookkeeping.access_credentials IS 'MUST be encrypted via Edge Function before storage';
COMMENT ON COLUMN farms.bookkeeping.bas_lodgement_frequency IS 'Business Activity Statement frequency for compliance tracking';
COMMENT ON COLUMN farms.bookkeeping.accounting_method IS 'Cash or Accrual accounting method used by the business';
COMMENT ON COLUMN farms.bookkeeping.financial_year_end_month IS 'Month when financial year ends (1-12, where 1=January, 12=December)';
COMMENT ON COLUMN farms.bookkeeping.chart_of_accounts_setup IS 'How the chart of accounts was configured';
COMMENT ON COLUMN farms.bookkeeping.bas_preparation IS 'Who prepares the Business Activity Statements';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.bookkeeping FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.payroll (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_3_id UUID NOT NULL UNIQUE REFERENCES farms.step_3_financial_systems(id) ON DELETE CASCADE,
    
    is_payroll_processing_needed BOOLEAN NOT NULL,
    employee_count INTEGER,
    current_payroll_software TEXT,
    payroll_frequency TEXT, -- e.g., 'Weekly', 'Fortnightly', 'Monthly'
    
    -- Access management
    is_access_to_software_granted BOOLEAN NOT NULL DEFAULT false,
    encrypted_access_credentials BYTEA,
    
    -- Compliance tracking
    superannuation_fund TEXT,
    workers_compensation_policy TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_employee_count CHECK (employee_count IS NULL OR employee_count >= 0),
    CONSTRAINT chk_payroll_logic CHECK (
        (is_payroll_processing_needed = false) OR 
        (is_payroll_processing_needed = true AND employee_count > 0)
    )
);

COMMENT ON TABLE farms.payroll IS 'Comprehensive payroll processing needs and system access details';
COMMENT ON COLUMN farms.payroll.encrypted_access_credentials IS 'Encrypted payroll system credentials';
CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.payroll FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.assets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_3_id UUID NOT NULL REFERENCES farms.step_3_financial_systems(id) ON DELETE CASCADE,
    
    asset_category farms.asset_category_enum NOT NULL,
    asset_type TEXT NOT NULL,
    
    -- Common fields
    make_or_provider TEXT,
    registration_or_policy_number TEXT,
    renewal_date DATE,
    
    -- Vehicle/Equipment specific
    model_year INTEGER,
    serial_number TEXT,
    purchase_date DATE,
    purchase_price DECIMAL(10,2),
    
    -- Insurance specific  
    policy_type TEXT, -- e.g., 'Public Liability', 'Vehicle', 'Workers Comp', 'Crop Insurance'
    coverage_amount DECIMAL(12,2),
    excess_amount DECIMAL(8,2),
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    -- Conditional validation based on asset category
    CONSTRAINT chk_vehicle_equipment_fields CHECK (
        asset_category = 'Insurance' OR 
        (asset_category IN ('Vehicle', 'Equipment') AND registration_or_policy_number IS NOT NULL)
    ),
    CONSTRAINT chk_insurance_fields CHECK (
        asset_category != 'Insurance' OR 
        (asset_category = 'Insurance' AND policy_type IS NOT NULL AND renewal_date IS NOT NULL)
    ),
    CONSTRAINT chk_positive_amounts CHECK (
        (purchase_price IS NULL OR purchase_price >= 0) AND
        (coverage_amount IS NULL OR coverage_amount >= 0) AND
        (excess_amount IS NULL OR excess_amount >= 0)
    )
);

COMMENT ON TABLE farms.assets IS 'Unified asset registry for vehicles, equipment, and insurance with category-specific validation';
COMMENT ON COLUMN farms.assets.policy_type IS 'For insurance: Public Liability, Vehicle, Workers Comp, Crop/Livestock insurance';
COMMENT ON COLUMN farms.assets.registration_or_policy_number IS 'Vehicle registration or insurance policy number';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.assets FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_assets_step_3_id ON farms.assets(step_3_id);
CREATE INDEX idx_assets_category ON farms.assets(asset_category);
CREATE INDEX idx_assets_renewal ON farms.assets(renewal_date) WHERE renewal_date IS NOT NULL;

CREATE TABLE farms.external_accountant (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_3_id UUID NOT NULL UNIQUE REFERENCES farms.step_3_financial_systems(id) ON DELETE CASCADE,

    firm_name TEXT NOT NULL,
    contact_person TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    address TEXT,

    -- Service details
    services_provided TEXT[],
    engagement_type TEXT, -- e.g., 'Annual Tax Return', 'Monthly Bookkeeping', 'BAS Preparation', 'Full Service'
    fee_structure TEXT,

    -- Access and integration
    has_software_access BOOLEAN NOT NULL DEFAULT false,
    software_access_level TEXT,
    preferred_communication_method farms.communication_method_enum,

    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

    CONSTRAINT chk_accountant_email_format CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'),
    CONSTRAINT chk_accountant_phone_format CHECK (phone IS NULL OR phone ~ '^\\+61\\d{9}$'),
    CONSTRAINT chk_engagement_type CHECK (engagement_type IS NULL OR engagement_type IN (
        'Annual Tax Return', 'Monthly Bookkeeping', 'BAS Preparation', 'Full Service', 'Consultation Only', 'Other'
    )),
    CONSTRAINT chk_software_access_level CHECK (software_access_level IS NULL OR software_access_level IN (
        'View Only', 'Edit Access', 'Full Admin', 'None'
    ))
);

COMMENT ON TABLE farms.external_accountant IS 'External accounting service provider details and engagement information';
COMMENT ON COLUMN farms.external_accountant.services_provided IS 'Array of services (e.g., [Tax Returns, BAS, Bookkeeping, Payroll])';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.external_accountant FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.banking_info (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_3_id UUID NOT NULL REFERENCES farms.step_3_financial_systems(id) ON DELETE CASCADE,

    bank_name TEXT NOT NULL,
    account_type TEXT NOT NULL,
    account_purpose TEXT NOT NULL,

    -- Encrypted sensitive data
    encrypted_account_details BYTEA,

    -- Bank feed integration
    has_bank_feeds BOOLEAN NOT NULL DEFAULT false,
    bank_feed_provider TEXT,
    feed_frequency TEXT,

    -- Relationship details
    relationship_manager TEXT,
    branch_location TEXT,

    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

    CONSTRAINT chk_account_type CHECK (account_type IN (
        'Business Transaction', 'Business Savings', 'Term Deposit', 'Line of Credit',
        'Equipment Finance', 'Other'
    )),
    CONSTRAINT chk_account_purpose CHECK (account_purpose IN (
        'Primary Operating', 'Tax Savings', 'Equipment Purchase', 'Emergency Fund',
        'Investment', 'Payroll', 'Other'
    )),
    CONSTRAINT chk_feed_frequency CHECK (feed_frequency IS NULL OR feed_frequency IN (
        'Daily', 'Weekly', 'Monthly'
    ))
);

COMMENT ON TABLE farms.banking_info IS 'Banking relationships and account management details';
COMMENT ON COLUMN farms.banking_info.encrypted_account_details IS 'MUST be encrypted bank account details via Edge Function';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.banking_info FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_banking_info_step_3_id ON farms.banking_info(step_3_id);

CREATE TABLE farms.financial_reporting_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_3_id UUID NOT NULL UNIQUE REFERENCES farms.step_3_financial_systems(id) ON DELETE CASCADE,

    -- Reporting requirements
    required_reports TEXT[] NOT NULL,
    reporting_frequency farms.reporting_frequency_enum NOT NULL,
    financial_year_end DATE NOT NULL,

    -- KPI tracking
    key_performance_indicators TEXT[],
    benchmark_targets JSONB,

    -- Compliance requirements
    regulatory_requirements TEXT[],
    audit_frequency TEXT,

    -- Stakeholder reporting
    stakeholder_reports TEXT[],
    board_reporting_required BOOLEAN NOT NULL DEFAULT false,

    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

    CONSTRAINT chk_required_reports_not_empty CHECK (array_length(required_reports, 1) > 0),
    CONSTRAINT chk_audit_frequency CHECK (audit_frequency IS NULL OR audit_frequency IN (
        'Annual', 'Biennial', 'Triennial', 'As Required', 'None'
    ))
);

COMMENT ON TABLE farms.financial_reporting_config IS 'Financial reporting requirements and KPI tracking configuration';
COMMENT ON COLUMN farms.financial_reporting_config.required_reports IS 'Array of required reports (e.g., [P&L, Balance Sheet, Cash Flow, Budget vs Actual])';
COMMENT ON COLUMN farms.financial_reporting_config.benchmark_targets IS 'JSON object containing KPI targets and benchmarks';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.financial_reporting_config FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.integration_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_3_id UUID NOT NULL UNIQUE REFERENCES farms.step_3_financial_systems(id) ON DELETE CASCADE,

    -- System integration preferences
    preferred_integrations TEXT[],
    existing_software_systems TEXT[],

    -- Data synchronization
    sync_frequency TEXT NOT NULL,
    auto_sync_enabled BOOLEAN NOT NULL DEFAULT true,

    -- API and connectivity
    api_access_required BOOLEAN NOT NULL DEFAULT false,
    webhook_endpoints TEXT[],

    -- Backup and security
    backup_frequency TEXT NOT NULL,
    data_retention_period_months INTEGER NOT NULL DEFAULT 84, -- 7 years

    -- Notification preferences
    sync_notifications_enabled BOOLEAN NOT NULL DEFAULT true,
    error_notification_method farms.communication_method_enum,

    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

    CONSTRAINT chk_sync_frequency CHECK (sync_frequency IN (
        'Real-time', 'Hourly', 'Daily', 'Weekly', 'Manual'
    )),
    CONSTRAINT chk_backup_frequency CHECK (backup_frequency IN (
        'Daily', 'Weekly', 'Monthly'
    )),
    CONSTRAINT chk_retention_period CHECK (data_retention_period_months >= 12)
);

COMMENT ON TABLE farms.integration_settings IS 'System integration preferences and data synchronization configuration';
COMMENT ON COLUMN farms.integration_settings.preferred_integrations IS 'Array of preferred third-party integrations';
COMMENT ON COLUMN farms.integration_settings.data_retention_period_months IS 'Data retention period in months (minimum 12, default 84 for 7 years)';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.integration_settings FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

--==============================================================================
-- 5. STEP 4 CHILD TABLES: REGISTRATIONS & INSURANCE
--==============================================================================

CREATE TABLE farms.vehicle_registrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_4_id UUID NOT NULL REFERENCES farms.step_4_registrations_insurance(id) ON DELETE CASCADE,

    vehicle_type farms.vehicle_type_enum NOT NULL,
    make TEXT NOT NULL,
    model TEXT NOT NULL,
    year INTEGER,

    -- Registration details
    registration_number TEXT NOT NULL,
    registration_expiry DATE NOT NULL,
    registration_state TEXT NOT NULL,

    -- Vehicle identification
    vin_chassis_number TEXT,
    engine_number TEXT,

    -- Insurance and compliance
    insurance_policy_number TEXT,
    insurance_expiry DATE,
    roadworthy_certificate_expiry DATE,

    -- Usage and condition
    primary_use TEXT NOT NULL,
    current_condition TEXT,
    annual_km_estimate INTEGER,

    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

    CONSTRAINT chk_vehicle_year CHECK (
        year IS NULL OR (year >= 1900 AND year <= EXTRACT(YEAR FROM CURRENT_DATE) + 1)
    ),
    CONSTRAINT chk_registration_state CHECK (registration_state IN (
        'NSW', 'VIC', 'QLD', 'WA', 'SA', 'TAS', 'ACT', 'NT'
    )),
    CONSTRAINT chk_primary_use CHECK (primary_use IN (
        'Farm Operations', 'Transport', 'Personal Use', 'Commercial', 'Other'
    )),
    CONSTRAINT chk_condition CHECK (current_condition IS NULL OR current_condition IN (
        'Excellent', 'Good', 'Fair', 'Poor', 'Requires Repair'
    )),
    CONSTRAINT chk_positive_km CHECK (annual_km_estimate IS NULL OR annual_km_estimate >= 0),
    CONSTRAINT chk_future_expiry CHECK (registration_expiry > CURRENT_DATE)
);

COMMENT ON TABLE farms.vehicle_registrations IS 'Farm vehicle registration management with compliance tracking';
COMMENT ON COLUMN farms.vehicle_registrations.primary_use IS 'Primary purpose of vehicle usage for insurance and compliance';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.vehicle_registrations FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_vehicle_registrations_step_4_id ON farms.vehicle_registrations(step_4_id);
CREATE INDEX idx_vehicle_registrations_expiry ON farms.vehicle_registrations(registration_expiry);
CREATE INDEX idx_vehicle_registrations_type ON farms.vehicle_registrations(vehicle_type);

CREATE TABLE farms.insurance_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_4_id UUID NOT NULL REFERENCES farms.step_4_registrations_insurance(id) ON DELETE CASCADE,

    insurance_type farms.insurance_type_enum NOT NULL,
    policy_number TEXT NOT NULL,
    insurer_name TEXT NOT NULL,

    -- Coverage details
    coverage_amount DECIMAL(12,2),
    excess_amount DECIMAL(8,2),
    coverage_description TEXT,

    -- Policy dates
    policy_start_date DATE NOT NULL,
    policy_expiry_date DATE NOT NULL,

    -- Premium information
    annual_premium DECIMAL(10,2),
    payment_frequency TEXT,

    -- Contact and claims
    broker_contact TEXT,
    claims_contact TEXT,
    policy_conditions TEXT,

    -- Risk assessment
    risk_category TEXT,
    special_conditions TEXT[],

    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

    CONSTRAINT chk_positive_amounts CHECK (
        (coverage_amount IS NULL OR coverage_amount > 0) AND
        (excess_amount IS NULL OR excess_amount >= 0) AND
        (annual_premium IS NULL OR annual_premium > 0)
    ),
    CONSTRAINT chk_policy_dates CHECK (policy_expiry_date > policy_start_date),
    CONSTRAINT chk_payment_frequency CHECK (payment_frequency IS NULL OR payment_frequency IN (
        'Monthly', 'Quarterly', 'Semi-Annual', 'Annual'
    )),
    CONSTRAINT chk_risk_category CHECK (risk_category IS NULL OR risk_category IN (
        'Low', 'Medium', 'High', 'Very High'
    ))
);

COMMENT ON TABLE farms.insurance_policies IS 'Comprehensive insurance policy management with coverage tracking';
COMMENT ON COLUMN farms.insurance_policies.special_conditions IS 'Array of special policy conditions or exclusions';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.insurance_policies FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_insurance_policies_step_4_id ON farms.insurance_policies(step_4_id);
CREATE INDEX idx_insurance_policies_expiry ON farms.insurance_policies(policy_expiry_date);
CREATE INDEX idx_insurance_policies_type ON farms.insurance_policies(insurance_type);

-- Enhanced licenses table (migrated from Step 2 to Step 4) with correct enum values
CREATE TABLE farms.licenses_new (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_4_id UUID NOT NULL REFERENCES farms.step_4_registrations_insurance(id) ON DELETE CASCADE,

    license_type farms.license_type_enum NOT NULL,
    license_number TEXT,
    issuing_authority TEXT,
    issue_date DATE,
    expiry_date DATE,

    -- Enhanced fields with corrected enum values
    license_status farms.license_status_enum NOT NULL DEFAULT 'Current',
    renewal_frequency farms.renewal_frequency_enum,
    renewal_cost DECIMAL(8,2),

    -- Compliance tracking
    last_renewal_date DATE,
    next_renewal_reminder_date DATE,
    compliance_notes TEXT,

    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),

    CONSTRAINT chk_expiry_date CHECK (expiry_date IS NULL OR issue_date IS NULL OR expiry_date > issue_date),
    CONSTRAINT chk_renewal_cost CHECK (renewal_cost IS NULL OR renewal_cost >= 0),
    CONSTRAINT chk_renewal_dates CHECK (
        last_renewal_date IS NULL OR issue_date IS NULL OR last_renewal_date >= issue_date
    )
);

COMMENT ON TABLE farms.licenses_new IS 'Enhanced operational licenses with corrected status tracking and renewal management';
COMMENT ON COLUMN farms.licenses_new.license_status IS 'Current status: Current, Pending Renewal, Expired, Under Review';
COMMENT ON COLUMN farms.licenses_new.renewal_frequency IS 'Renewal schedule: Annual, Biennial, Every 3 Years, Every 5 Years, One-time only';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.licenses_new FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_licenses_new_step_4_id ON farms.licenses_new(step_4_id);
CREATE INDEX idx_licenses_new_expiry ON farms.licenses_new(expiry_date) WHERE expiry_date IS NOT NULL;
CREATE INDEX idx_licenses_new_status ON farms.licenses_new(license_status);

--==============================================================================
-- 6. STEP 5 CHILD TABLES: FINALIZATION & AGREEMENTS
--==============================================================================

CREATE TABLE farms.data_migration (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_5_id UUID NOT NULL UNIQUE REFERENCES farms.step_5_finalization(id) ON DELETE CASCADE,
    
    -- Current data storage assessment
    primary_cloud_storage TEXT NOT NULL,
    secondary_cloud_storage TEXT,
    filing_system_description TEXT NOT NULL,
    
    -- Document organization details
    folder_structure_description TEXT,
    document_categories TEXT[], -- e.g., ['Financial', 'Legal', 'Operational', 'Compliance']
    estimated_document_count INTEGER,
    total_estimated_storage_gb DECIMAL(8,2),
    
    -- Migration preferences
    migration_priority_order TEXT[], -- e.g., ['Financial Records', 'Legal Documents', 'Operational Data']
    preferred_migration_timeline TEXT NOT NULL, -- e.g., 'Immediate', 'Within 1 Month', 'Within 3 Months'
    data_cleanup_required BOOLEAN NOT NULL DEFAULT false,
    legacy_system_access_needed BOOLEAN NOT NULL DEFAULT false,
    
    -- Technical requirements
    requires_data_mapping BOOLEAN NOT NULL DEFAULT false,
    custom_field_mapping_needed BOOLEAN NOT NULL DEFAULT false,
    integration_complexity_level TEXT, -- 'Simple', 'Moderate', 'Complex'
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_cloud_storage CHECK (
        primary_cloud_storage IN ('Google Drive', 'Dropbox', 'OneDrive', 'SharePoint', 'Box', 'Local Storage', 'Other', 'None')
    ),
    CONSTRAINT chk_migration_timeline CHECK (
        preferred_migration_timeline IN ('Immediate', 'Within 1 Week', 'Within 1 Month', 'Within 3 Months', 'Within 6 Months')
    ),
    CONSTRAINT chk_complexity_level CHECK (
        integration_complexity_level IS NULL OR integration_complexity_level IN ('Simple', 'Moderate', 'Complex')
    ),
    CONSTRAINT chk_positive_storage CHECK (
        total_estimated_storage_gb IS NULL OR total_estimated_storage_gb >= 0
    ),
    CONSTRAINT chk_positive_document_count CHECK (
        estimated_document_count IS NULL OR estimated_document_count >= 0
    )
);

COMMENT ON TABLE farms.data_migration IS 'Comprehensive data migration planning and current storage assessment';
COMMENT ON COLUMN farms.data_migration.document_categories IS 'Array of document types for migration prioritization';
COMMENT ON COLUMN farms.data_migration.migration_priority_order IS 'Ordered array of migration priorities';
COMMENT ON COLUMN farms.data_migration.integration_complexity_level IS 'Assessment of technical complexity for migration planning';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.data_migration FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_5_id UUID NOT NULL REFERENCES farms.step_5_finalization(id) ON DELETE CASCADE,
    
    data_type TEXT NOT NULL,
    access_level TEXT NOT NULL,
    permission_granted BOOLEAN NOT NULL DEFAULT false,
    permission_granted_date TIMESTAMPTZ,
    permission_scope TEXT, -- 'Full Access', 'Limited Access', 'Emergency Only', 'Read Only'
    
    -- Enhanced permission tracking
    granted_by_contact_id UUID REFERENCES farms.contacts(id),
    requires_ongoing_consent BOOLEAN NOT NULL DEFAULT true,
    consent_renewal_frequency TEXT, -- 'Annual', 'Biennial', 'One-time', 'As Required'
    special_conditions TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT uq_permission_per_step UNIQUE (step_5_id, data_type),
    CONSTRAINT chk_data_type CHECK (data_type IN (
        'Bookkeeping Software', 'Cloud Storage', 'Bank Feeds', 'Payroll System', 
        'Document Storage', 'Email Systems', 'Financial Records', 'Operational Data'
    )),
    CONSTRAINT chk_access_level CHECK (access_level IN ('View Only', 'Edit Access', 'Full Admin', 'None')),
    CONSTRAINT chk_permission_scope CHECK (permission_scope IS NULL OR permission_scope IN (
        'Full Access', 'Limited Access', 'Emergency Only', 'Read Only'
    )),
    CONSTRAINT chk_consent_renewal CHECK (consent_renewal_frequency IS NULL OR consent_renewal_frequency IN (
        'Annual', 'Biennial', 'One-time', 'As Required'
    )),
    CONSTRAINT chk_permission_completion CHECK (
        (permission_granted = false) OR 
        (permission_granted = true AND permission_granted_date IS NOT NULL)
    )
);

COMMENT ON TABLE farms.permissions IS 'Enhanced granular permission matrix for data access authorization with consent tracking';
COMMENT ON COLUMN farms.permissions.access_level IS 'Technical access level: View Only, Edit Access, Full Admin, None';
COMMENT ON COLUMN farms.permissions.permission_scope IS 'Business scope of permission for clarity';
COMMENT ON COLUMN farms.permissions.granted_by_contact_id IS 'Which contact granted this permission';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.permissions FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_permissions_step_5_id ON farms.permissions(step_5_id);
CREATE INDEX idx_permissions_contact_id ON farms.permissions(granted_by_contact_id);

CREATE TABLE farms.agreements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_5_id UUID NOT NULL REFERENCES farms.step_5_finalization(id) ON DELETE CASCADE,
    
    agreement_type farms.agreement_type_enum NOT NULL,
    is_agreed BOOLEAN NOT NULL DEFAULT false,
    
    -- Enhanced signature tracking
    signature_data TEXT, -- Base64 signature data (legacy support)
    signature_storage_path TEXT, -- Path to signature file in storage
    signature_method TEXT, -- 'Digital Signature', 'Electronic Consent', 'Wet Signature', 'Verbal Consent'
    signatory_name TEXT,
    signatory_title TEXT,
    signatory_authority_level TEXT, -- 'Authorized Signatory', 'Director', 'Owner', 'Delegated Authority'
    
    -- Agreement tracking
    agreed_at TIMESTAMPTZ,
    agreement_version TEXT DEFAULT '1.0',
    agreement_document_path TEXT, -- Path to the agreement document
    witness_name TEXT,
    witness_signature_path TEXT,
    
    -- Legal compliance
    ip_address INET, -- IP address for audit trail
    user_agent TEXT, -- Browser/device info for audit trail
    consent_method TEXT, -- 'Online Form', 'Email', 'Phone', 'In Person'
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT uq_agreement_type_per_step UNIQUE (step_5_id, agreement_type),
    CONSTRAINT chk_signature_method CHECK (signature_method IS NULL OR signature_method IN (
        'Digital Signature', 'Electronic Consent', 'Wet Signature', 'Verbal Consent'
    )),
    CONSTRAINT chk_signatory_authority CHECK (signatory_authority_level IS NULL OR signatory_authority_level IN (
        'Authorized Signatory', 'Director', 'Owner', 'Delegated Authority', 'General Manager'
    )),
    CONSTRAINT chk_consent_method CHECK (consent_method IS NULL OR consent_method IN (
        'Online Form', 'Email', 'Phone', 'In Person', 'Postal'
    )),
    CONSTRAINT chk_agreement_completion CHECK (
        (is_agreed = false) OR 
        (is_agreed = true AND agreed_at IS NOT NULL AND signatory_name IS NOT NULL AND
         (signature_data IS NOT NULL OR signature_storage_path IS NOT NULL))
    )
);

COMMENT ON TABLE farms.agreements IS 'Enhanced legal agreements and consent tracking with comprehensive audit trail';
COMMENT ON COLUMN farms.agreements.signature_storage_path IS 'Preferred: Path to signature file in secure storage';
COMMENT ON COLUMN farms.agreements.signature_data IS 'Legacy: Base64 signature data (use storage_path instead)';
COMMENT ON COLUMN farms.agreements.signatory_authority_level IS 'Authority level of the person signing';
COMMENT ON COLUMN farms.agreements.ip_address IS 'IP address for legal audit trail';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.agreements FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_agreements_step_5_id ON farms.agreements(step_5_id);
CREATE INDEX idx_agreements_signatory ON farms.agreements(signatory_name);

CREATE TABLE farms.payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_5_id UUID NOT NULL UNIQUE REFERENCES farms.step_5_finalization(id) ON DELETE CASCADE,
    
    -- Payment method setup
    payment_method TEXT DEFAULT 'Direct Debit',
    payment_frequency TEXT DEFAULT 'Monthly',
    
    -- Bank account details (encrypted)
    bank_account_details BYTEA NOT NULL, -- Encrypted bank details
    backup_payment_method TEXT,
    backup_payment_details BYTEA, -- Encrypted backup payment details
    
    -- Payment preferences
    preferred_payment_date INTEGER, -- Day of month (1-28)
    payment_notification_email TEXT,
    payment_notification_method farms.communication_method_enum DEFAULT 'Email',
    
    -- Authorization and setup
    direct_debit_authority_signed BOOLEAN NOT NULL DEFAULT false,
    direct_debit_authority_date TIMESTAMPTZ,
    authorized_by_contact_id UUID REFERENCES farms.contacts(id),
    
    -- Payment terms
    payment_terms_accepted BOOLEAN NOT NULL DEFAULT false,
    payment_terms_version TEXT DEFAULT '1.0',
    late_payment_fee_accepted BOOLEAN NOT NULL DEFAULT false,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_payment_method CHECK (payment_method IN (
        'Direct Debit', 'Credit Card', 'Bank Transfer', 'PayPal', 'Other'
    )),
    CONSTRAINT chk_backup_payment_method CHECK (backup_payment_method IS NULL OR backup_payment_method IN (
        'Credit Card', 'Bank Transfer', 'PayPal', 'Manual Invoice'
    )),
    CONSTRAINT chk_payment_frequency CHECK (payment_frequency IN (
        'Weekly', 'Fortnightly', 'Monthly', 'Quarterly', 'Annually'
    )),
    CONSTRAINT chk_payment_date CHECK (
        preferred_payment_date IS NULL OR (preferred_payment_date >= 1 AND preferred_payment_date <= 28)
    ),
    CONSTRAINT chk_payment_notification_email CHECK (
        payment_notification_email IS NULL OR 
        payment_notification_email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'
    ),
    CONSTRAINT chk_direct_debit_completion CHECK (
        (payment_method != 'Direct Debit') OR 
        (payment_method = 'Direct Debit' AND direct_debit_authority_signed = true AND direct_debit_authority_date IS NOT NULL)
    )
);

COMMENT ON TABLE farms.payments IS 'Comprehensive payment method setup with authorization tracking and backup options';
COMMENT ON COLUMN farms.payments.bank_account_details IS 'MUST be encrypted bank account details via Edge Function';
COMMENT ON COLUMN farms.payments.preferred_payment_date IS 'Preferred day of month for payment processing (1-28)';
COMMENT ON COLUMN farms.payments.authorized_by_contact_id IS 'Contact who authorized the payment setup';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.payments FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_payments_authorized_by ON farms.payments(authorized_by_contact_id);

CREATE TABLE farms.communication_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_5_id UUID NOT NULL UNIQUE REFERENCES farms.step_5_finalization(id) ON DELETE CASCADE,
    
    -- Primary communication setup
    preferred_methods farms.communication_method_enum[] NOT NULL,
    preferred_contact_times TEXT NOT NULL,
    reporting_frequency farms.reporting_frequency_enum NOT NULL,
    
    -- Detailed communication preferences
    emergency_contact_method farms.communication_method_enum,
    business_hours_only BOOLEAN NOT NULL DEFAULT true,
    timezone TEXT DEFAULT 'Australia/Sydney',
    
    -- Communication channels setup
    primary_email TEXT NOT NULL,
    secondary_email TEXT,
    primary_phone TEXT,
    secondary_phone TEXT,
    whatsapp_number TEXT,
    
    -- Report and notification preferences
    financial_reports_email TEXT,
    compliance_alerts_email TEXT,
    system_notifications_email TEXT,
    
    -- Frequency and timing preferences
    preferred_report_day_of_month INTEGER, -- Day of month for regular reports (1-28)
    preferred_contact_day_of_week INTEGER, -- Day of week for check-ins (1=Monday, 7=Sunday)
    avoid_contact_periods TEXT[], -- e.g., ['Harvest Season', 'Planting Season']
    
    -- Special requirements
    accessibility_requirements TEXT,
    language_preference TEXT DEFAULT 'English',
    cultural_considerations TEXT,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_preferred_methods_not_empty CHECK (array_length(preferred_methods, 1) > 0),
    CONSTRAINT chk_primary_email_format CHECK (
        primary_email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'
    ),
    CONSTRAINT chk_secondary_email_format CHECK (
        secondary_email IS NULL OR secondary_email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'
    ),
    CONSTRAINT chk_financial_reports_email_format CHECK (
        financial_reports_email IS NULL OR financial_reports_email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'
    ),
    CONSTRAINT chk_compliance_alerts_email_format CHECK (
        compliance_alerts_email IS NULL OR compliance_alerts_email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'
    ),
    CONSTRAINT chk_system_notifications_email_format CHECK (
        system_notifications_email IS NULL OR system_notifications_email ~* '^[A-Za-z0-9._+%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$'
    ),
    CONSTRAINT chk_phone_format CHECK (
        primary_phone IS NULL OR primary_phone ~ '^\\+61\\d{9}$'
    ),
    CONSTRAINT chk_secondary_phone_format CHECK (
        secondary_phone IS NULL OR secondary_phone ~ '^\\+61\\d{9}$'
    ),
    CONSTRAINT chk_whatsapp_format CHECK (
        whatsapp_number IS NULL OR whatsapp_number ~ '^\\+61\\d{9}$'
    ),
    CONSTRAINT chk_report_day CHECK (
        preferred_report_day_of_month IS NULL OR (preferred_report_day_of_month >= 1 AND preferred_report_day_of_month <= 28)
    ),
    CONSTRAINT chk_contact_day CHECK (
        preferred_contact_day_of_week IS NULL OR (preferred_contact_day_of_week >= 1 AND preferred_contact_day_of_week <= 7)
    ),
    CONSTRAINT chk_language_preference CHECK (
        language_preference IN ('English', 'Mandarin', 'Cantonese', 'Arabic', 'Italian', 'Greek', 'Vietnamese', 'Other')
    )
);

COMMENT ON TABLE farms.communication_preferences IS 'Comprehensive communication preferences with multi-channel support and cultural considerations';
COMMENT ON COLUMN farms.communication_preferences.preferred_methods IS 'Array of communication methods in order of preference';
COMMENT ON COLUMN farms.communication_preferences.emergency_contact_method IS 'Preferred method for urgent communications';
COMMENT ON COLUMN farms.communication_preferences.avoid_contact_periods IS 'Array of periods when contact should be avoided';
COMMENT ON COLUMN farms.communication_preferences.accessibility_requirements IS 'Any special accessibility needs for communication';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.communication_preferences FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();

CREATE TABLE farms.finalization_submission (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    step_5_id UUID NOT NULL UNIQUE REFERENCES farms.step_5_finalization(id) ON DELETE CASCADE,
    
    -- Submission tracking
    submission_status TEXT NOT NULL DEFAULT 'In Progress',
    submitted_at TIMESTAMPTZ,
    submitted_by_contact_id UUID REFERENCES farms.contacts(id),
    
    -- Validation and completeness
    data_completeness_score DECIMAL(5,2), -- Percentage of required data completed
    validation_errors TEXT[],
    validation_warnings TEXT[],
    manual_review_required BOOLEAN NOT NULL DEFAULT false,
    manual_review_reason TEXT,
    
    -- Final confirmations
    terms_and_conditions_accepted BOOLEAN NOT NULL DEFAULT false,
    privacy_policy_accepted BOOLEAN NOT NULL DEFAULT false,
    service_agreement_accepted BOOLEAN NOT NULL DEFAULT false,
    data_processing_consent_given BOOLEAN NOT NULL DEFAULT false,
    
    -- Submission metadata
    submission_ip_address INET,
    submission_user_agent TEXT,
    submission_location TEXT, -- Optional: City/Region for audit
    
    -- Processing status
    internal_processing_status TEXT DEFAULT 'Pending Review',
    assigned_to_staff_member TEXT,
    processing_notes TEXT,
    estimated_setup_completion_date DATE,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    
    CONSTRAINT chk_submission_status CHECK (submission_status IN (
        'In Progress', 'Submitted', 'Under Review', 'Approved', 'Rejected', 'Requires Clarification'
    )),
    CONSTRAINT chk_data_completeness CHECK (
        data_completeness_score IS NULL OR (data_completeness_score >= 0 AND data_completeness_score <= 100)
    ),
    CONSTRAINT chk_internal_processing_status CHECK (internal_processing_status IN (
        'Pending Review', 'In Review', 'Approved', 'Setup In Progress', 'Setup Complete', 'On Hold'
    )),
    CONSTRAINT chk_submission_completion CHECK (
        (submission_status = 'In Progress') OR 
        (submission_status != 'In Progress' AND submitted_at IS NOT NULL AND submitted_by_contact_id IS NOT NULL)
    ),
    CONSTRAINT chk_final_confirmations CHECK (
        (submission_status = 'In Progress') OR 
        (submission_status != 'In Progress' AND 
         terms_and_conditions_accepted = true AND 
         privacy_policy_accepted = true AND 
         service_agreement_accepted = true AND 
         data_processing_consent_given = true)
    )
);

COMMENT ON TABLE farms.finalization_submission IS 'Final submission tracking with validation, consent, and processing status';
COMMENT ON COLUMN farms.finalization_submission.data_completeness_score IS 'Percentage of required data fields completed';
COMMENT ON COLUMN farms.finalization_submission.validation_errors IS 'Array of validation errors that must be resolved';
COMMENT ON COLUMN farms.finalization_submission.internal_processing_status IS 'Internal workflow status for staff tracking';

CREATE TRIGGER set_timestamp BEFORE UPDATE ON farms.finalization_submission FOR EACH ROW EXECUTE PROCEDURE public.trigger_set_timestamp();
CREATE INDEX idx_finalization_submission_status ON farms.finalization_submission(submission_status);
CREATE INDEX idx_finalization_submission_processing ON farms.finalization_submission(internal_processing_status);
CREATE INDEX idx_finalization_submitted_by ON farms.finalization_submission(submitted_by_contact_id);

/*
================================================================================
-- END OF COMPLETE SCHEMA DEFINITION
--
-- Summary of Enhancements:
-- 1. Added comprehensive enum types for data validation
-- 2. Included all missing data attributes from requirements
-- 3. Enhanced contact management with proper categorization
-- 4. Separated asset types with conditional validation
-- 5. Added detailed chemical and license tracking
-- 6. Comprehensive communication preferences
-- 7. Enhanced documentation and comments throughout
-- 8. Strategic indexing for performance
-- 9. Proper constraint validation for business rules
-- 10. Complete coverage of all business requirements
================================================================================
*/
