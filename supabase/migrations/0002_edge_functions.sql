/*
--------------------------------------------------------------------------------
-- File: farms_schema_edge_functions.sql
--
-- Version: 2.1 - Fixed document upload function
--
-- Description:
-- Updated to fix document upload issues and improve error handling
--------------------------------------------------------------------------------
*/

--==============================================================================
-- SECTION 1: POSTGRESQL FUNCTIONS (PL/pgSQL)
--==============================================================================

-- Clean up any existing functions first
DROP FUNCTION IF EXISTS public.get_onboarding_session_data();
DROP FUNCTION IF EXISTS public.create_user_onboarding_session_with_steps();
DROP FUNCTION IF EXISTS public.finalize_document_upload(json);
DROP FUNCTION IF EXISTS public.update_encrypted_field(text, uuid, text, bytea);

--------------------------------------------------------------------------------
-- Function: get_onboarding_session_data()
--
-- V3 Description:
-- This is the primary function for the "resume onboarding" feature, completely
-- rewritten for the V3 5-step architecture. It securely fetches all data,
-- organized by the five onboarding steps, and aggregates it into a single,
-- structured, and nested JSON object.
--
-- Usecase:
-- The frontend calls this function via RPC to repopulate the entire wizard.
-- The nested JSON structure maps directly to the UI's component hierarchy.
--
-- Returns: A single nested JSON object or NULL if no session exists.
--------------------------------------------------------------------------------
CREATE OR REPLACE FUNCTION public.get_onboarding_session_data()
RETURNS json AS $$
DECLARE
    session_data json;
    v_onboarding_session_id UUID;
    v_step_1_id UUID;
    v_step_2_id UUID;
    v_step_3_id UUID;
    v_step_4_id UUID;
    v_step_5_id UUID;
BEGIN
    -- 1. Get the session ID for the currently authenticated user
    SELECT id INTO v_onboarding_session_id
    FROM farms.onboarding_sessions
    WHERE user_id = auth.uid()
    LIMIT 1;

    -- 2. If no session exists, return NULL
    IF v_onboarding_session_id IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- 3. Get the IDs for each of the five step parent tables
    SELECT id INTO v_step_1_id FROM farms.step_1_business_profile WHERE onboarding_session_id = v_onboarding_session_id;
    SELECT id INTO v_step_2_id FROM farms.step_2_farm_operations WHERE onboarding_session_id = v_onboarding_session_id;
    SELECT id INTO v_step_3_id FROM farms.step_3_financial_systems WHERE onboarding_session_id = v_onboarding_session_id;
    SELECT id INTO v_step_4_id FROM farms.step_4_registrations_insurance WHERE onboarding_session_id = v_onboarding_session_id;
    SELECT id INTO v_step_5_id FROM farms.step_5_finalization WHERE onboarding_session_id = v_onboarding_session_id;

    -- 4. Aggregate all related data into a single, nested JSON object with 5-step structure
    SELECT json_build_object(
        'onboardingSession', (SELECT to_jsonb(os) FROM farms.onboarding_sessions os WHERE id = v_onboarding_session_id),
        'step1_businessProfile', json_build_object(
            'id', v_step_1_id,
            'businessRegistration', (SELECT to_jsonb(br) FROM farms.business_registration br WHERE step_1_id = v_step_1_id),
            'addresses', COALESCE((SELECT json_agg(to_jsonb(a)) FROM farms.addresses a WHERE step_1_id = v_step_1_id), '[]'::json),
            'contacts', COALESCE((SELECT json_agg(to_jsonb(c)) FROM farms.contacts c WHERE step_1_id = v_step_1_id), '[]'::json),
            'keyStaff', COALESCE((SELECT json_agg(to_jsonb(ks)) FROM farms.key_staff ks WHERE step_1_id = v_step_1_id), '[]'::json)
        ),
        'step2_farmOperations', json_build_object(
            'id', v_step_2_id,
            'farmInfo', (SELECT to_jsonb(fi) FROM farms.farm_info fi WHERE step_2_id = v_step_2_id),
            'farmBlocks', COALESCE((SELECT json_agg(to_jsonb(fb)) FROM farms.farm_blocks fb WHERE step_2_id = v_step_2_id), '[]'::json),
            'activities', COALESCE((SELECT json_agg(to_jsonb(act)) FROM farms.activities act WHERE step_2_id = v_step_2_id), '[]'::json),
            'suppliers', COALESCE((SELECT json_agg(to_jsonb(s)) FROM farms.suppliers s WHERE step_2_id = v_step_2_id), '[]'::json),
            'contracts', COALESCE((SELECT json_agg(to_jsonb(c)) FROM farms.contracts c WHERE step_2_id = v_step_2_id), '[]'::json),
            'chemicalUsage', COALESCE((SELECT json_agg(to_jsonb(cu)) FROM farms.chemical_usage cu WHERE step_2_id = v_step_2_id), '[]'::json)
        ),
        'step3_financialSystems', json_build_object(
            'id', v_step_3_id,
            'bookkeeping', (SELECT to_jsonb(b) FROM farms.bookkeeping b WHERE step_3_id = v_step_3_id),
            'payroll', (SELECT to_jsonb(p) FROM farms.payroll p WHERE step_3_id = v_step_3_id),
            'assets', COALESCE((SELECT json_agg(to_jsonb(a)) FROM farms.assets a WHERE step_3_id = v_step_3_id), '[]'::json),
            'externalAccountant', (SELECT to_jsonb(ea) FROM farms.external_accountant ea WHERE step_3_id = v_step_3_id),
            'bankingInfo', COALESCE((SELECT json_agg(to_jsonb(bi)) FROM farms.banking_info bi WHERE step_3_id = v_step_3_id), '[]'::json),
            'financialReportingConfig', (SELECT to_jsonb(frc) FROM farms.financial_reporting_config frc WHERE step_3_id = v_step_3_id),
            'integrationSettings', (SELECT to_jsonb(is_tbl) FROM farms.integration_settings is_tbl WHERE step_3_id = v_step_3_id)
        ),
        'step4_registrationsInsurance', json_build_object(
            'id', v_step_4_id,
            'vehicleRegistrations', COALESCE((SELECT json_agg(to_jsonb(vr)) FROM farms.vehicle_registrations vr WHERE step_4_id = v_step_4_id), '[]'::json),
            'insurancePolicies', COALESCE((SELECT json_agg(to_jsonb(ip)) FROM farms.insurance_policies ip WHERE step_4_id = v_step_4_id), '[]'::json),
            'licenses', COALESCE((SELECT json_agg(to_jsonb(l)) FROM farms.licenses_new l WHERE step_4_id = v_step_4_id), '[]'::json)
        ),
        'step5_finalization', json_build_object(
            'id', v_step_5_id,
            'dataMigration', (SELECT to_jsonb(dm) FROM farms.data_migration dm WHERE step_5_id = v_step_5_id),
            'permissions', COALESCE((SELECT json_agg(to_jsonb(p)) FROM farms.permissions p WHERE step_5_id = v_step_5_id), '[]'::json),
            'agreements', COALESCE((SELECT json_agg(to_jsonb(a)) FROM farms.agreements a WHERE step_5_id = v_step_5_id), '[]'::json),
            'payments', (SELECT to_jsonb(p) FROM farms.payments p WHERE step_5_id = v_step_5_id),
            'communicationPreferences', (SELECT to_jsonb(cp) FROM farms.communication_preferences cp WHERE step_5_id = v_step_5_id),
            'finalizationSubmission', (SELECT to_jsonb(fs) FROM farms.finalization_submission fs WHERE step_5_id = v_step_5_id)
        ),
        'documents', COALESCE((SELECT json_agg(to_jsonb(d)) FROM farms.documents d WHERE onboarding_session_id = v_onboarding_session_id), '[]'::json)
    ) INTO session_data;

    RETURN session_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.get_onboarding_session_data() IS 'V3: Returns complete hierarchical session data for 5-step onboarding structure with all child tables included.';


--------------------------------------------------------------------------------
-- Function: finalize_document_upload(json) - FIXED VERSION
--
-- V2.1 Description:
-- Fixed version with better error handling and validation
--------------------------------------------------------------------------------
CREATE OR REPLACE FUNCTION public.finalize_document_upload(payload json)
RETURNS UUID AS $$
DECLARE
    v_session_id UUID;
    v_user_id UUID;
    v_document_id UUID;
    v_document_name TEXT;
    v_related_to_entity TEXT;
    v_related_to_id UUID;
    v_file_path TEXT;
    v_payload_text TEXT;
BEGIN
    -- Log the incoming payload for debugging
    v_payload_text := payload::TEXT;
    RAISE NOTICE 'finalize_document_upload called with payload: %', v_payload_text;
    
    -- Validate payload is not null
    IF payload IS NULL THEN
        RAISE EXCEPTION 'Payload cannot be null';
    END IF;

    -- Extract and validate payload fields with enhanced error handling
    BEGIN
        -- Extract fields with explicit null checks
        v_session_id := CASE 
            WHEN payload->>'sessionId' IS NULL OR payload->>'sessionId' = '' THEN NULL
            ELSE (payload->>'sessionId')::UUID
        END;
        
        v_document_name := COALESCE(NULLIF(TRIM(payload->>'documentName'), ''), NULL);
        v_related_to_entity := COALESCE(NULLIF(TRIM(payload->>'relatedToEntity'), ''), NULL);
        
        v_related_to_id := CASE 
            WHEN payload->>'relatedToId' IS NULL OR payload->>'relatedToId' = '' THEN NULL
            ELSE (payload->>'relatedToId')::UUID
        END;
        
        v_file_path := COALESCE(NULLIF(TRIM(payload->>'filePath'), ''), NULL);
        
        RAISE NOTICE 'Extracted fields: sessionId=%, documentName=%, relatedToEntity=%, relatedToId=%, filePath=%', 
            v_session_id, v_document_name, v_related_to_entity, v_related_to_id, v_file_path;
            
    EXCEPTION
        WHEN invalid_text_representation THEN
            RAISE EXCEPTION 'Invalid UUID format in payload. sessionId and relatedToId must be valid UUIDs. Error: %', SQLERRM;
        WHEN OTHERS THEN
            RAISE EXCEPTION 'Invalid payload format or structure: %', SQLERRM;
    END;

    -- Comprehensive field validation with specific error messages
    IF v_session_id IS NULL THEN
        RAISE EXCEPTION 'Missing or invalid sessionId in payload - must be a valid UUID';
    END IF;
    
    IF v_document_name IS NULL THEN
        RAISE EXCEPTION 'Missing or empty documentName in payload - must be a non-empty string';
    END IF;
    
    IF v_related_to_entity IS NULL THEN
        RAISE EXCEPTION 'Missing or empty relatedToEntity in payload - must be a non-empty string';
    END IF;
    
    IF v_related_to_id IS NULL THEN
        RAISE EXCEPTION 'Missing or invalid relatedToId in payload - must be a valid UUID';
    END IF;
    
    IF v_file_path IS NULL THEN
        RAISE EXCEPTION 'Missing or empty filePath in payload - must be a non-empty string';
    END IF;

    -- Verify the session exists and get the user_id
    -- Note: The calling Edge Function has already verified that the authenticated user owns this session,
    -- so we don't need to check auth.uid() here. This function is SECURITY DEFINER and is designed
    -- to be called by trusted Edge Functions that perform their own authentication.
    BEGIN
        SELECT user_id INTO v_user_id
        FROM farms.onboarding_sessions
        WHERE id = v_session_id;
        
        RAISE NOTICE 'Session lookup completed: sessionId=%, foundUserId=%', v_session_id, v_user_id;
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE EXCEPTION 'Error during session lookup for sessionId %: %', v_session_id, SQLERRM;
    END;

    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Session not found: %', v_session_id;
    END IF;

    -- Insert the new document record with enhanced error handling
    BEGIN
        RAISE NOTICE 'Attempting to insert document record with sessionId=%, documentName=%, relatedToEntity=%, relatedToId=%, filePath=%', 
            v_session_id, v_document_name, v_related_to_entity, v_related_to_id, v_file_path;
            
        INSERT INTO farms.documents (
            onboarding_session_id, 
            document_name, 
            related_to_entity, 
            related_to_id, 
            storage_bucket_path
        )
        VALUES (
            v_session_id,
            v_document_name,
            v_related_to_entity,
            v_related_to_id,
            v_file_path
        )
        RETURNING id INTO v_document_id;

        RAISE NOTICE 'Document insert completed successfully with documentId=%', v_document_id;
        
    EXCEPTION
        WHEN unique_violation THEN
            RAISE EXCEPTION 'Document with the same details already exists for this session';
        WHEN foreign_key_violation THEN
            RAISE EXCEPTION 'Invalid reference: session ID % or related entity ID % does not exist', v_session_id, v_related_to_id;
        WHEN check_violation THEN
            RAISE EXCEPTION 'Data validation failed: one or more field values violate database constraints';
        WHEN OTHERS THEN
            RAISE EXCEPTION 'Failed to insert document record: %', SQLERRM;
    END;

    -- Final validation that the insert was successful
    IF v_document_id IS NULL THEN
        RAISE EXCEPTION 'Document insert completed but no document ID was returned - this indicates a database issue';
    END IF;

    RAISE NOTICE 'finalize_document_upload completed successfully with documentId=%', v_document_id;
    RETURN v_document_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.finalize_document_upload(json) IS 'V2.1: Enhanced version with better error handling and validation for document upload finalization. Designed to be called by Edge Functions that perform their own authentication.';


--------------------------------------------------------------------------------
-- Function: update_encrypted_field(text, uuid, text, bytea)
--
-- V3 Description:
-- This function securely updates an encrypted field in a specified table within
-- the new V3 hierarchical schema. It intelligently determines the correct "step"
-- parent table to join through, ensuring data is updated correctly while
-- maintaining strict ownership checks.
--
-- Security:
-- The function validates table/field names against an allow-list and derives
-- the join path server-side to prevent malicious use.
--------------------------------------------------------------------------------
CREATE OR REPLACE FUNCTION public.update_encrypted_field(
    p_table_name text,
    p_session_id uuid,
    p_field_name text,
    p_encrypted_value bytea
)
RETURNS boolean AS $$
DECLARE
    v_user_id UUID;
    v_query text;
    v_step_table_name text;
    v_step_fk_column text;
    v_allowed_tables text[] := ARRAY['bookkeeping', 'payments', 'payroll'];
    v_allowed_fields text[] := ARRAY['access_credentials', 'bank_account_details', 'encrypted_access_credentials'];
BEGIN
    -- Validate table and field names against allow-lists to prevent SQL injection
    IF NOT (p_table_name = ANY(v_allowed_tables)) THEN
        RAISE EXCEPTION 'Invalid table name';
    END IF;

    IF NOT (p_field_name = ANY(v_allowed_fields)) THEN
        RAISE EXCEPTION 'Invalid field name';
    END IF;

    -- Determine the correct step parent table and FK based on the target table (UPDATED FOR 5-STEP)
    CASE p_table_name
        WHEN 'bookkeeping' THEN
            v_step_table_name := 'step_3_financial_systems';
            v_step_fk_column := 'step_3_id';
        WHEN 'payments' THEN
            v_step_table_name := 'step_5_finalization'; -- UPDATED: was step_4_agreements
            v_step_fk_column := 'step_5_id'; -- UPDATED: was step_4_id
        WHEN 'payroll' THEN
            v_step_table_name := 'step_3_financial_systems';
            v_step_fk_column := 'step_3_id';
        ELSE
            RAISE EXCEPTION 'Configuration error: No step mapping for table %', p_table_name;
    END CASE;

    -- Verify the calling user owns the session
    SELECT user_id INTO v_user_id
    FROM farms.onboarding_sessions
    WHERE id = p_session_id;

    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Session not found';
    END IF;

    -- Construct and execute the update query using the hierarchical structure
    v_query := format(
        'UPDATE farms.%I SET %I = $1 WHERE %I IN (SELECT id FROM farms.%I WHERE onboarding_session_id = $2)',
        p_table_name,       -- The target table (e.g., farms.bookkeeping)
        p_field_name,       -- The target field (e.g., access_credentials)
        v_step_fk_column,   -- The foreign key column in the target table (e.g., step_3_id)
        v_step_table_name   -- The parent step table to join against (e.g., step_3_financial_systems)
    );

    EXECUTE v_query USING p_encrypted_value, p_session_id;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.update_encrypted_field(text, uuid, text, bytea) IS 'V3: Updated for 5-step structure - securely updates encrypted fields with correct step parent table mappings.';


--==============================================================================
-- SECTION 2: SUPABASE EDGE FUNCTION SPECIFICATIONS (DENO/TYPESCRIPT)
-- V2: These specifications are updated to reflect the new table names.
--==============================================================================

/*
--------------------------------------------------------------------------------
-- Edge Function: `validate-australian-business-number`
-- V2 Notes: No change to logic. Interacts with external ABN lookup service.
--------------------------------------------------------------------------------
*/


/*
--------------------------------------------------------------------------------
-- Edge Function: `initiate-secure-file-upload`
--
-- V2 Notes:
-- The `relatedToEntity` will now use the new, simplified table names (e.g., "licenses", "assets").
-- The file path structure remains the same logical hierarchy.
--
-- Request Payload (JSON):
-- {
--   "sessionId": "UUID of the onboarding session",
--   "fileName": "my-license.pdf",
--   "fileType": "application/pdf",
--   "relatedToEntity": "licenses", -- V2: Use new table name
--   "relatedToId": "UUID of the license record"
-- }
--
-- Core Logic (V2):
-- ...
-- 4. Construct the hierarchical file path: `onboarding_sessions/{sessionId}/{relatedToEntity}/{relatedToId}/{uuidv4()}-{sanitizedFileName}`.
-- ...
--------------------------------------------------------------------------------
*/


/*
--------------------------------------------------------------------------------
-- Edge Function: `finalize-secure-file-upload`
--
-- V2 Notes:
-- The RPC call to `public.finalize_document_upload` remains the same.
-- The `relatedToEntity` field in the payload should use the new table name.
--
-- Request Payload (JSON):
-- {
--   "sessionId": "...",
--   "documentName": "my-license.pdf",
--   "relatedToEntity": "licenses", -- V2: Use new table name
--   "relatedToId": "...",
--   "filePath": "onboarding_sessions/..."
-- }
--------------------------------------------------------------------------------
*/


/*
--------------------------------------------------------------------------------
-- Edge Function: `import-asset-registry-from-csv`
--
-- V2 Notes:
-- The function will now insert into the `farms.assets` table.
-- The `step_3_id` will need to be retrieved first before inserting.
--
-- Core Logic (V2):
-- ...
-- 4. Get the user's `onboarding_session_id` AND the `step_3_id` for that session.
-- 5. Construct an array of new asset objects, ensuring each has the correct `step_3_id`.
-- 6. Perform a batch `.insert()` into the `farms.assets` table.
-- ...
--------------------------------------------------------------------------------
*/


/*
--------------------------------------------------------------------------------
-- Edge Function: `process-digital-signature-and-consent`
--
-- V2 Notes:
-- The function will now perform an `UPSERT` on the `farms.agreements` table.
-- The `step_4_id` will be required for the `WHERE` clause of the upsert.
--
-- Request Payload (JSON):
-- {
--   "sessionId": "...",
--   "agreementType": "Service Agreement",
--   "signatureData": "data:image/png;base64,..."
-- }
--
-- Core Logic (V2):
-- ...
-- 4. On successful upload, perform an `UPSERT` on `farms.agreements` where
--    `step_4_id` matches the session and `agreement_type` matches the input.
--------------------------------------------------------------------------------
*/


/*
--------------------------------------------------------------------------------
-- Edge Function: `encrypt-and-store-sensitive-field`
--
-- V2 Notes:
-- The `tableName` in the payload should be the new simple name (e.g., "bookkeeping").
-- The backend RPC call to `update_encrypted_field` is updated, but the
-- Edge Function's external API remains the same.
--
-- Request Payload (JSON):
-- {
--   "tableName": "bookkeeping", -- V2: Use new table name
--   "sessionId": "...",
--   "fieldName": "access_credentials",
--   "plainTextValue": "sensitive data here"
-- }
--------------------------------------------------------------------------------
*/


/*
--------------------------------------------------------------------------------
-- Edge Function: `cleanup-orphan-storage-files`
-- V2 Notes: No change to logic. It cross-references the storage bucket with
-- the central `farms.documents` table, which has not changed its role.
--------------------------------------------------------------------------------
*/

--------------------------------------------------------------------------------
-- Function: create_user_onboarding_session_with_steps()
--
-- V3 Description:
-- Creates complete 5-step onboarding session with all parent records and returns full session data structure.
--
-- This function is atomic - either all records are created successfully or
-- the entire transaction is rolled back. It handles race conditions by
-- checking for existing sessions first.
--
-- Returns: Complete session data JSON or NULL if creation fails
--------------------------------------------------------------------------------
CREATE OR REPLACE FUNCTION public.create_user_onboarding_session_with_steps()
RETURNS json AS $$
DECLARE
    v_user_id UUID := auth.uid();
    v_session_id UUID;
    v_step_1_id UUID;
    v_step_2_id UUID;
    v_step_3_id UUID;
    v_step_4_id UUID;
    v_step_5_id UUID;
    v_existing_session_id UUID;
BEGIN
    -- 1. Verify user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to create onboarding session';
    END IF;

    -- 2. Check if user already has a session (race condition protection)
    SELECT id INTO v_existing_session_id
    FROM farms.onboarding_sessions
    WHERE user_id = v_user_id
    LIMIT 1;

    -- If session already exists, return the existing data
    IF v_existing_session_id IS NOT NULL THEN
        RETURN (SELECT public.get_onboarding_session_data());
    END IF;

    -- 3. Create the main onboarding session
    INSERT INTO farms.onboarding_sessions (user_id, status, current_step)
    VALUES (v_user_id, 'in_progress', 1)
    RETURNING id INTO v_session_id;

    -- 4. Create all five step parent records atomically
    INSERT INTO farms.step_1_business_profile (onboarding_session_id)
    VALUES (v_session_id)
    RETURNING id INTO v_step_1_id;

    INSERT INTO farms.step_2_farm_operations (onboarding_session_id)
    VALUES (v_session_id)
    RETURNING id INTO v_step_2_id;

    INSERT INTO farms.step_3_financial_systems (onboarding_session_id)
    VALUES (v_session_id)
    RETURNING id INTO v_step_3_id;

    INSERT INTO farms.step_4_registrations_insurance (onboarding_session_id)
    VALUES (v_session_id)
    RETURNING id INTO v_step_4_id;

    INSERT INTO farms.step_5_finalization (onboarding_session_id)
    VALUES (v_session_id)
    RETURNING id INTO v_step_5_id;

    -- 5. Return the complete session data using the existing function
    RETURN (SELECT public.get_onboarding_session_data());

EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise with context
        RAISE EXCEPTION 'Failed to create onboarding session with 5 steps: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.create_user_onboarding_session_with_steps() IS 'V3: Creates complete 5-step onboarding session with all parent records and returns full session data structure.';


--------------------------------------------------------------------------------
-- Function: delete_document(uuid, uuid)
--
-- Description:
-- Securely deletes a document record from the farms.documents table.
-- This function validates that the user owns the session and that the
-- document belongs to that session before performing the deletion.
--
-- Parameters:
-- - p_document_id: UUID of the document to delete
-- - p_session_id: UUID of the onboarding session (for authorization)
--
-- Returns: UUID of the deleted document or raises exception if not found/unauthorized
--
-- Security:
-- This function is SECURITY DEFINER and performs strict authorization checks.
-- The calling Edge Function must authenticate the user and verify session ownership.
--------------------------------------------------------------------------------
CREATE OR REPLACE FUNCTION public.delete_document(
    p_document_id UUID,
    p_session_id UUID
)
RETURNS UUID AS $$
DECLARE
    v_user_id UUID;
    v_document_session_id UUID;
    v_document_storage_path TEXT;
    v_deleted_document_id UUID;
BEGIN
    -- 1. Validate input parameters
    IF p_document_id IS NULL THEN
        RAISE EXCEPTION 'Document ID cannot be null';
    END IF;
    
    IF p_session_id IS NULL THEN
        RAISE EXCEPTION 'Session ID cannot be null';
    END IF;

    RAISE NOTICE 'delete_document called with documentId=%, sessionId=%', p_document_id, p_session_id;

    -- 2. Verify the session exists and get the user_id
    SELECT user_id INTO v_user_id
    FROM farms.onboarding_sessions
    WHERE id = p_session_id;

    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Session not found: %', p_session_id;
    END IF;

    RAISE NOTICE 'Session found: sessionId=%, userId=%', p_session_id, v_user_id;

    -- 3. Verify the document exists and belongs to the session
    SELECT onboarding_session_id, storage_bucket_path 
    INTO v_document_session_id, v_document_storage_path
    FROM farms.documents
    WHERE id = p_document_id;

    IF v_document_session_id IS NULL THEN
        RAISE EXCEPTION 'Document not found: %', p_document_id;
    END IF;

    -- 4. Verify the document belongs to the provided session (authorization check)
    IF v_document_session_id != p_session_id THEN
        RAISE EXCEPTION 'Document does not belong to the specified session. Document session: %, Provided session: %', 
            v_document_session_id, p_session_id;
    END IF;

    RAISE NOTICE 'Document authorization verified: documentId=%, sessionId=%, storagePath=%', 
        p_document_id, p_session_id, v_document_storage_path;

    -- 5. Delete the document record
    DELETE FROM farms.documents
    WHERE id = p_document_id
    RETURNING id INTO v_deleted_document_id;

    IF v_deleted_document_id IS NULL THEN
        RAISE EXCEPTION 'Failed to delete document record: %', p_document_id;
    END IF;

    RAISE NOTICE 'Document successfully deleted: documentId=%', v_deleted_document_id;
    
    RETURN v_deleted_document_id;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error deleting document: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.delete_document(uuid, uuid) IS 'Securely deletes a document record after verifying user authorization through session ownership. Designed to be called by Edge Functions that perform authentication.';
