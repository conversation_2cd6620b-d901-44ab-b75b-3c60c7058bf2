import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

Deno.serve(async (req: Request) => {
    // Handle CORS preflight requests
    if (req.method === 'OPTIONS') {
        return new Response('ok', { headers: corsHeaders })
    }

    try {
        // Get Supabase credentials from environment variables
        const supabaseUrl = Deno.env.get('SUPABASE_URL') as string
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') as string

        if (!supabaseUrl || !supabaseServiceKey) {
            throw new Error('Missing Supabase environment variables')
        }

        // Create Supabase client with service role key (admin privileges)
        const supabase = createClient(supabaseUrl, supabaseServiceKey)

        // Get authorization header from request
        const authHeader = req.headers.get('Authorization')
        if (!authHeader) {
            return new Response(
                JSON.stringify({ error: 'Missing authorization header' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        // Authenticate the user from the JWT token
        const token = authHeader.replace('Bearer ', '')
        const { data: { user }, error: authError } = await supabase.auth.getUser(token)

        if (authError || !user) {
            return new Response(
                JSON.stringify({ error: 'Unauthorized' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 401 }
            )
        }

        // Parse request payload
        const { sessionId } = await req.json()

        // Validate input
        if (!sessionId) {
            return new Response(
                JSON.stringify({ error: 'Missing required field: sessionId' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Fetch full onboarding session data using the hierarchical structure
        const { data: sessionData, error: sessionError } = await supabase
            .schema('farms')
            .from('onboarding_sessions')
            .select('id, user_id, status, current_step')
            .eq('id', sessionId)
            .eq('user_id', user.id)
            .single()

        if (sessionError || !sessionData) {
            return new Response(
                JSON.stringify({ error: 'Session not found or access denied' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 404 }
            )
        }

        // Use the RPC function to get all session data for validation
        const { data: fullSessionData, error: rpcError } = await supabase.rpc('get_onboarding_session_data')

        if (rpcError || !fullSessionData) {
            console.error('Error fetching session data via RPC:', rpcError)
            return new Response(
                JSON.stringify({ error: 'Failed to fetch complete session data for validation' }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
            )
        }

        // UPDATED: Data Validation using the new 5-step hierarchical structure
        const validationErrors: string[] = []

        // Validate Step 1: Business Profile
        const step1Data = fullSessionData.step1_businessProfile
        if (!step1Data?.businessRegistration) {
            validationErrors.push("Step 1: Business registration details are required")
        } else {
            const bizReg = step1Data.businessRegistration
            if (!bizReg.full_business_name?.trim()) {
                validationErrors.push("Step 1: Full Business Name is required")
            }
            if (!bizReg.abn?.trim()) {
                validationErrors.push("Step 1: ABN is required")
            }
            if (!bizReg.business_structure?.trim()) {
                validationErrors.push("Step 1: Business Structure is required")
            }
        }

        if (!step1Data?.contacts || step1Data.contacts.length === 0) {
            validationErrors.push("Step 1: At least one contact is required")
        } else {
            const mainContact = step1Data.contacts.find(c => c.contact_type === 'Primary Contact')
            if (!mainContact) {
                validationErrors.push("Step 1: A 'Primary Contact' is required")
            } else {
                if (!mainContact.name?.trim()) {
                    validationErrors.push("Step 1: Primary Contact name is required")
                }
                if (!mainContact.email?.trim() || !/^[\S]+@[\S]+\.[\S]+$/.test(mainContact.email)) {
                    validationErrors.push("Step 1: Primary Contact requires a valid email")
                }
            }
        }

        if (!step1Data?.addresses || step1Data.addresses.length === 0) {
            validationErrors.push("Step 1: At least one address is required")
        } else {
            const propertyAddress = step1Data.addresses.find(a => a.address_type === 'Property Address')
            if (!propertyAddress?.full_address_text?.trim()) {
                validationErrors.push("Step 1: Property address is required")
            }
        }

        // Validate Step 2: Farm Operations
        const step2Data = fullSessionData.step2_farmOperations
        if (!step2Data?.activities || step2Data.activities.length === 0) {
            validationErrors.push("Step 2: At least one farming activity is required")
        }

        // Validate Step 3: Financial Systems
        const step3Data = fullSessionData.step3_financialSystems
        if (!step3Data?.bookkeeping) {
            validationErrors.push("Step 3: Bookkeeping information is required")
        } else {
            if (!step3Data.bookkeeping.current_software?.trim()) {
                validationErrors.push("Step 3: Current bookkeeping software is required")
            }
            if (!step3Data.bookkeeping.bas_lodgement_frequency?.trim()) {
                validationErrors.push("Step 3: BAS lodgement frequency is required")
            }
        }

        if (!step3Data?.payroll) {
            validationErrors.push("Step 3: Payroll information is required")
        }

        // UPDATED: Validate Step 4: Registrations & Insurance
        const step4Data = fullSessionData.step4_registrationsInsurance
        if (step4Data?.vehicleRegistrations && step4Data.vehicleRegistrations.length > 0) {
            const invalidVehicles = step4Data.vehicleRegistrations.filter(v =>
                !v.vehicle_type?.trim() || !v.registration_number?.trim() || !v.registration_expiry
            )
            if (invalidVehicles.length > 0) {
                validationErrors.push("Step 4: All vehicle registrations must have type, number, and expiry date")
            }
        }

        if (step4Data?.insurancePolicies && step4Data.insurancePolicies.length > 0) {
            const invalidPolicies = step4Data.insurancePolicies.filter(p =>
                !p.insurance_type?.trim() || !p.policy_number?.trim() || !p.policy_expiry_date
            )
            if (invalidPolicies.length > 0) {
                validationErrors.push("Step 4: All insurance policies must have type, number, and expiry date")
            }
        }

        // UPDATED: Validate Step 5: Finalization
        const step5Data = fullSessionData.step5_finalization
        if (!step5Data?.dataMigration) {
            validationErrors.push("Step 5: Data migration information is required")
        } else {
            if (!step5Data.dataMigration.primary_cloud_storage?.trim()) {
                validationErrors.push("Step 5: Primary cloud storage selection is required")
            }
            if (!step5Data.dataMigration.filing_system_description?.trim()) {
                validationErrors.push("Step 5: Filing system description is required")
            }
        }

        if (!step5Data?.communicationPreferences) {
            validationErrors.push("Step 5: Communication preferences are required")
        } else {
            const commPrefs = step5Data.communicationPreferences
            if (!commPrefs.preferred_methods || commPrefs.preferred_methods.length === 0) {
                validationErrors.push("Step 5: At least one communication method must be selected")
            }
            if (!commPrefs.preferred_contact_times?.trim()) {
                validationErrors.push("Step 5: Preferred contact times are required")
            }
            if (!commPrefs.reporting_frequency?.trim()) {
                validationErrors.push("Step 5: Reporting frequency is required")
            }
        }

        if (!step5Data?.payments?.bank_account_details) {
            validationErrors.push("Step 5: Bank account details are required")
        }

        // Check for Service Agreement signature
        if (!step5Data?.agreements || step5Data.agreements.length === 0) {
            validationErrors.push("Step 5: Service Agreement must be signed")
        } else {
            const serviceAgreement = step5Data.agreements.find(a => a.agreement_type === 'Service Agreement')
            if (!serviceAgreement?.is_agreed || !serviceAgreement?.signature_storage_path) {
                validationErrors.push("Step 5: Service Agreement must be agreed to and signed")
            }
        }

        if (validationErrors.length > 0) {
            return new Response(
                JSON.stringify({
                    success: false,
                    error: 'Validation failed',
                    errors: validationErrors,
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
            )
        }

        // Check if already completed
        if (sessionData.status === 'completed') {
            return new Response(
                JSON.stringify({
                    success: true,
                    message: 'Onboarding session was already completed'
                }),
                { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
            )
        }

        // UPDATED: Update session status to completed with 5-step structure
        const { error: updateError } = await supabase
            .schema('farms')
            .from('onboarding_sessions')
            .update({
                status: 'completed',
                current_step: 5 // UPDATED: was 5, now correctly reflects 5-step completion
            })
            .eq('id', sessionId)

        if (updateError) {
            throw new Error(`Failed to update session status: ${updateError.message}`)
        }

        return new Response(
            JSON.stringify({
                success: true,
                message: 'Onboarding session submitted and completed successfully'
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )

    } catch (error) {
        console.error('Error in finalize-onboarding-submission:', error)
        return new Response(
            JSON.stringify({
                success: false,
                error: `Error finalizing onboarding submission: ${error.message}`
            }),
            { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        )
    }
}) 