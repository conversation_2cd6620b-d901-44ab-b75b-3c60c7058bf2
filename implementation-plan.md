# **Frontend-Backend Consolidation Work Breakdown**
## NewTerra Onboarding System: 4-Step → 5-Step Migration

---

## **DRAFT 1: Basic Structural Update**

### **Phase 1: Core Infrastructure Updates**

#### **1.1 Navigation & Routing Updates**
- **File:** `src/pages/Onboarding.tsx`
  - Update STEPS array from 4 to 5 steps
  - Add new Step 4: "Registrations & Insurance"
  - Rename Step 5: "Agreements & Review" → "Finalization"
  - Update navigation logic for 5-step flow

#### **1.2 Database Type System Updates**
- **File:** `src/types/database.types.ts`
  - Regenerate types from updated Supabase schema
  - Ensure all new tables and enums are included

- **File:** `src/types/onboarding.ts`
  - Add Step 4 and Step 5 type definitions
  - Update SessionData interface for 5-step structure

#### **1.3 Context Updates**
- **File:** `src/contexts/OnboardingContext.tsx`
  - Add `ensureStep4RegistrationsInsuranceRecordExists`
  - Add `ensureStep5FinalizationRecordExists`
  - Update session data structure mapping
  - Update helper functions for 5-step access

### **Phase 2: Component Creation & Migration**

#### **2.1 Create New Step 4 Component**
- **New File:** `src/components/onboarding/steps/RegistrationsInsuranceStep.tsx`
  - Vehicle registrations management
  - Insurance policies management
  - Enhanced licenses management (migrated from Step 2)

#### **2.2 Create Step 4 Form Components**
- **New File:** `src/components/form_components/VehicleRegistrationForm.tsx`
- **New File:** `src/components/form_components/InsurancePolicyForm.tsx`
- **Update File:** `src/components/form_components/LicenseForm.tsx` (enhance for new fields)

#### **2.3 Rename & Update Step 5**
- **Rename:** `AgreementsStep.tsx` → `FinalizationStep.tsx`
- Update all internal references and table mappings

### **Phase 3: Utility & Helper Updates**

#### **3.1 Update Form Management Helpers**
- **File:** `src/utils/form-management-helpers.ts`
  - Update STEP_COLUMN_MAPPING for new table structure
  - Add Step 4 and Step 5 table mappings

#### **3.2 Update Context Helpers**
- **File:** `src/utils/onboarding-context-helpers.ts`
  - Add Step 4 and Step 5 data extraction functions
  - Update `getStepId` for 5-step structure

---

## **DRAFT 2: Enhanced Component Architecture**

### **Phase 1: Strategic Infrastructure Overhaul**

#### **1.1 Smart Navigation System**
- **File:** `src/pages/Onboarding.tsx`
  - Implement dynamic step configuration system
  - Add step validation gateway before navigation
  - Create progress persistence with 5-step tracking
  - Update breadcrumb and progress indicators

#### **1.2 Enhanced Type System**
- **File:** `src/types/form-components.ts`
  - Add VehicleRegistrationFormData interface
  - Add InsurancePolicyFormData interface
  - Add enhanced LicenseFormData with new fields
  - Update all step-specific form data types

- **File:** `src/types/onboarding.ts`
  - Create Step4RegistrationsData interface
  - Create Step5FinalizationData interface
  - Update hierarchical SessionData with proper nesting

#### **1.3 Context Architecture Enhancement**
- **File:** `src/contexts/OnboardingContext.tsx`
  - Implement generic step initialization pattern
  - Add step-specific data refresh functions
  - Create unified error handling for all steps
  - Add migration utility functions for data transitions

### **Phase 2: Component System Redesign**

#### **2.1 Step 4: Registrations & Insurance Hub**
- **File:** `src/components/onboarding/steps/RegistrationsInsuranceStep.tsx`
  - Implement tabbed interface for three sections:
    - Vehicle Registrations tab
    - Insurance Policies tab  
    - Enhanced Licenses tab (migrated from Step 2)
  - Use EntitySection pattern for consistent UI
  - Implement cross-tab data validation

#### **2.2 Enhanced Form Components with Reusability**
- **File:** `src/components/form_components/VehicleRegistrationForm.tsx`
  - Australian registration validation
  - Expiry date tracking and alerts
  - Integration with insurance policy linking

- **File:** `src/components/form_components/InsurancePolicyForm.tsx`
  - Multi-type insurance support (vehicle, liability, crop, etc.)
  - Premium calculation helpers
  - Policy renewal tracking

- **File:** `src/components/form_components/EnhancedLicenseForm.tsx` (rename from LicenseForm)
  - Enhanced status tracking (Current, Pending Renewal, Expired)
  - Renewal frequency management
  - Compliance alert system

#### **2.3 Step 5: Finalization Orchestration**
- **File:** `src/components/onboarding/steps/FinalizationStep.tsx` (renamed from AgreementsStep)
  - Update table references from step_4_id to step_5_id
  - Add completion validation across all previous steps
  - Implement final submission workflow

### **Phase 3: Hook System Evolution**

#### **3.1 Enhanced Step Initialization**
- **File:** `src/hooks/use-enhanced-step-init.ts`
  - Add Step 4 and Step 5 initialization logic
  - Implement step dependency validation
  - Add migration checks for data consistency

#### **3.2 Specialized Entity Management**
- **File:** `src/hooks/use-entity-list-management.ts`
  - Add vehicle registration entity management
  - Add insurance policy entity management
  - Enhanced license management with new fields

#### **3.3 Data Migration Hooks**
- **New File:** `src/hooks/use-data-migration.ts`
  - Handle license migration from Step 2 to Step 4
  - Handle agreements migration from Step 4 to Step 5
  - Provide migration status and rollback capabilities

### **Phase 4: Utility System Enhancement**

#### **4.1 Enhanced Validation System**
- **File:** `src/utils/onboarding-validation.ts`
  - Add vehicle registration validation
  - Add insurance policy validation
  - Enhanced license validation with new status fields

#### **4.2 Business Constants Expansion**
- **File:** `src/utils/business-constants.ts`
  - Add VEHICLE_TYPES, INSURANCE_TYPES constants
  - Add LICENSE_STATUS, RENEWAL_FREQUENCY constants
  - Create cross-referencing lookup tables

---

## **DRAFT 3: Production-Ready Strategic Implementation**

### **Phase 1: Foundation & Architecture Modernization**

#### **1.1 Dynamic Step Configuration System**
- **File:** `src/pages/Onboarding.tsx`
  ```typescript
  const STEP_CONFIG = [
    { id: 1, title: "Business Profile", component: BusinessProfileStep, tables: ['business_registration', 'contacts', 'addresses', 'key_staff'] },
    { id: 2, title: "Farm Operations", component: FarmOperationsStep, tables: ['activities', 'suppliers', 'contracts', 'chemical_usage'] },
    { id: 3, title: "Financial Systems", component: FinancialSystemsStep, tables: ['bookkeeping', 'payroll', 'assets'] },
    { id: 4, title: "Registrations & Insurance", component: RegistrationsInsuranceStep, tables: ['vehicle_registrations', 'insurance_policies', 'licenses_new'] },
    { id: 5, title: "Finalization", component: FinalizationStep, tables: ['data_migration', 'permissions', 'agreements', 'payments', 'communication_preferences'] }
  ];
  ```
  - Implement step validation pipeline with dependency checking
  - Add progressive disclosure with completion gates
  - Create step-specific loading and error boundaries

#### **1.2 Enhanced Type System Architecture**
- **File:** `src/types/database.types.ts` (auto-regenerated)
- **File:** `src/types/onboarding.ts`
  ```typescript
  interface Step4RegistrationsData {
    vehicleRegistrations: VehicleRegistration[];
    insurancePolicies: InsurancePolicy[];
    licenses: EnhancedLicense[];
  }
  
  interface Step5FinalizationData {
    dataMigration: DataMigration;
    permissions: Permission[];
    agreements: Agreement[];
    payments: Payment;
    communicationPreferences: CommunicationPreferences;
    finalizationSubmission: FinalizationSubmission;
  }
  ```

#### **1.3 Context System Overhaul**
- **File:** `src/contexts/OnboardingContext.tsx`
  - Implement generic step factory pattern:
  ```typescript
  const ensureStepRecordExists = async (stepNumber: number, sessionId: string) => {
    const stepTable = `step_${stepNumber}_${STEP_CONFIG[stepNumber-1].key}`;
    // Generic implementation
  };
  ```
  - Add step transition validation
  - Implement data consistency checks across step boundaries
  - Add rollback capabilities for failed migrations

### **Phase 2: Component Architecture Excellence**

#### **2.1 Step 4: Comprehensive Registrations & Insurance Management**
- **File:** `src/components/onboarding/steps/RegistrationsInsuranceStep.tsx`
  ```typescript
  export const RegistrationsInsuranceStep = () => {
    const { effectiveStepId } = useEnhancedStepInit({
      stepName: 'RegistrationsInsuranceStep',
      stepNumber: 4,
      ensureStepFunction: ensureStep4RegistrationsInsuranceRecordExists,
    });

    return (
      <StepContainer>
        <TabsContainer defaultValue="vehicles">
          <TabsList>
            <TabsTrigger value="vehicles">Vehicle Registrations</TabsTrigger>
            <TabsTrigger value="insurance">Insurance Policies</TabsTrigger>
            <TabsTrigger value="licenses">Licenses & Permits</TabsTrigger>
          </TabsList>
          
          <TabsContent value="vehicles">
            <EntitySection<VehicleRegistrationFormData>
              title="Vehicle Registrations"
              description="Manage farm vehicle registrations and compliance"
              entityManagement={vehicleManagement}
              FormComponent={VehicleRegistrationForm}
              emptyStateMessage="No vehicles registered yet"
            />
          </TabsContent>
          {/* Similar for insurance and licenses */}
        </TabsContainer>
      </StepContainer>
    );
  };
  ```

#### **2.2 Advanced Form Components with Cross-Integration**
- **File:** `src/components/form_components/VehicleRegistrationForm.tsx`
  ```typescript
  export const VehicleRegistrationForm: EntityFormComponent<VehicleRegistrationFormData> = ({ entity, onUpdate, onDelete }) => {
    const { formData, updateField, errors } = useFormManagement({
      entityId: entity.id,
      tableName: 'vehicle_registrations',
      initialData: entity,
      validationSchema: vehicleRegistrationSchema,
    });

    return (
      <FormCard>
        <FormField label="Vehicle Type" error={errors.vehicle_type}>
          <Select value={formData.vehicle_type} onValueChange={(value) => updateField('vehicle_type', value)}>
            {VEHICLE_TYPES.map(type => (
              <SelectItem key={type} value={type}>{type}</SelectItem>
            ))}
          </Select>
        </FormField>
        
        <ExpiryDateField
          label="Registration Expiry"
          value={formData.registration_expiry}
          onChange={(date) => updateField('registration_expiry', date)}
          alertThresholdDays={30}
        />
        
        <InsuranceLinkingField
          vehicleId={entity.id}
          currentInsurancePolicy={formData.insurance_policy_id}
          onPolicyLink={(policyId) => updateField('insurance_policy_id', policyId)}
        />
      </FormCard>
    );
  };
  ```

- **File:** `src/components/form_components/InsurancePolicyForm.tsx`
  - Multi-type insurance support with conditional fields
  - Premium calculation and renewal tracking
  - Asset linking for comprehensive coverage tracking

- **File:** `src/components/form_components/EnhancedLicenseForm.tsx`
  - Status lifecycle management (Current → Pending Renewal → Expired)
  - Automated renewal reminders
  - Compliance tracking with audit trail

#### **2.3 Step 5: Finalization Excellence**
- **File:** `src/components/onboarding/steps/FinalizationStep.tsx`
  - Update all step_4_id references to step_5_id
  - Add completion validation across all previous steps
  - Implement final submission workflow with comprehensive validation
  - Add submission preview and confirmation system

### **Phase 3: Hook System Mastery**

#### **3.1 Enhanced Step Initialization with Migration Support**
- **File:** `src/hooks/use-enhanced-step-init.ts`
  ```typescript
  export const useEnhancedStepInit = ({ stepName, stepNumber, ensureStepFunction, migrationChecks = [] }) => {
    // Add step dependency validation
    // Implement migration checks for data consistency
    // Add step transition guards
  };
  ```

#### **3.2 Specialized Entity Management Enhancement**
- **File:** `src/hooks/use-entity-list-management.ts`
  - Add step-aware entity management
  - Implement cross-entity validation (e.g., vehicle-insurance linking)
  - Add entity lifecycle management (creation, active, archived)

#### **3.3 Data Migration & Consistency Hooks**
- **New File:** `src/hooks/use-step-migration.ts`
  ```typescript
  export const useStepMigration = () => {
    const migrateLicensesFromStep2ToStep4 = async (sessionId: string) => {
      // Migration logic with validation and rollback
    };
    
    const migrateAgreementsFromStep4ToStep5 = async (sessionId: string) => {
      // Migration logic with validation and rollback
    };
    
    return { migrateLicensesFromStep2ToStep4, migrateAgreementsFromStep4ToStep5 };
  };
  ```

### **Phase 4: Utility System Excellence**

#### **4.1 Enhanced Validation Architecture**
- **File:** `src/utils/onboarding-validation.ts`
  ```typescript
  export const validateStep4 = (step4Data: Step4RegistrationsData): ValidationResult => {
    // Comprehensive Step 4 validation
    // Cross-entity validation (vehicle-insurance linking)
    // Compliance checking (registration expiry, insurance coverage)
  };
  
  export const validateStep5 = (step5Data: Step5FinalizationData): ValidationResult => {
    // Final submission validation
    // All previous steps completion check
    // Legal requirements validation
  };
  ```

#### **4.2 Enhanced Business Constants & Utilities**
- **File:** `src/utils/business-constants.ts`
  ```typescript
  export const STEP_4_CONSTANTS = {
    VEHICLE_TYPES: [...],
    INSURANCE_TYPES: [...],
    LICENSE_STATUSES: [...],
    RENEWAL_FREQUENCIES: [...]
  };
  
  export const CROSS_ENTITY_RULES = {
    vehicleInsuranceRequirements: { /* mapping rules */ },
    licenseRenewalThresholds: { /* alert thresholds */ }
  };
  ```

#### **4.3 Migration & Consistency Utilities**
- **New File:** `src/utils/migration-helpers.ts`
  ```typescript
  export const validateDataMigration = (fromStep: number, toStep: number, data: any): MigrationResult => {
    // Validate data structure compatibility
    // Check foreign key constraints
    // Ensure data integrity
  };
  
  export const createMigrationPlan = (sessionData: SessionData): MigrationPlan => {
    // Analyze current data state
    // Identify required migrations
    // Create execution plan with dependencies
  };
  ```

### **Phase 5: UI/UX Enhancement & Testing**

#### **5.1 Enhanced Step Indicator**
- **File:** `src/components/onboarding/StepIndicator.tsx`
  - Update for 5-step display
  - Add step completion indicators
  - Implement step dependency visualization

#### **5.2 Enhanced Navigation**
- **File:** `src/components/onboarding/OnboardingNavigation.tsx`
  - Add step validation gates
  - Implement smart navigation with completion checking
  - Add progress persistence

#### **5.3 Comprehensive Testing Updates**
- **New File:** `test/registrationsInsuranceStep.test.tsx`
- **New File:** `test/finalizationStep.test.tsx`
- **Update:** All existing test files for 5-step structure
- **New File:** `test/stepMigration.test.ts`

### **Phase 6: Performance & Security Optimization**

#### **6.1 Performance Enhancements**
- Implement step-specific lazy loading
- Add entity virtualization for large lists
- Optimize form auto-save patterns

#### **6.2 Security Updates**
- Update RLS policies for new step structure
- Add step transition authorization
- Implement audit logging for migrations

### **Phase 7: Documentation & Deployment**

#### **7.1 Documentation Updates**
- Update CLAUDE.md for 5-step architecture
- Create migration documentation
- Update API documentation

#### **7.2 Deployment Strategy**
- Create migration scripts for existing data
- Implement blue-green deployment for zero downtime
- Add rollback procedures

---

## **Critical Success Metrics**

1. **Zero Data Loss**: All existing data successfully migrated
2. **Backward Compatibility**: Existing sessions continue to function
3. **Performance Maintained**: No degradation in form auto-save or navigation
4. **Type Safety**: 100% TypeScript compliance maintained
5. **Test Coverage**: Comprehensive test suite for all new components

This production-ready plan ensures a seamless transition from 4-step to 5-step architecture while maintaining all existing functionality and improving the overall system architecture.