import { describe, it, expect } from 'vitest';
import { getStepData, getStepId, STEP_DATA_KEYS, STEP_ENSURE_FUNCTIONS } from '../src/utils/onboarding-context-helpers';
import type { FullOnboardingSessionData } from '../src/types/onboarding';

describe('5-Step Infrastructure Validation', () => {
  describe('Step Data Keys Mapping', () => {
    it('should have correct mapping for all 5 steps', () => {
      expect(STEP_DATA_KEYS[1]).toBe('step1_businessProfile');
      expect(STEP_DATA_KEYS[2]).toBe('step2_farmOperations');
      expect(STEP_DATA_KEYS[3]).toBe('step3_financialSystems');
      expect(STEP_DATA_KEYS[4]).toBe('step4_registrationsInsurance');
      expect(STEP_DATA_KEYS[5]).toBe('step5_finalization');
    });

    it('should have ensure function mapping for all 5 steps', () => {
      expect(STEP_ENSURE_FUNCTIONS[1]).toBe('ensureStep1BusinessProfileRecordExists');
      expect(STEP_ENSURE_FUNCTIONS[2]).toBe('ensureStep2FarmOperationsRecordExists');
      expect(STEP_ENSURE_FUNCTIONS[3]).toBe('ensureStep3FinancialSystemsRecordExists');
      expect(STEP_ENSURE_FUNCTIONS[4]).toBe('ensureStep4RegistrationsInsuranceRecordExists');
      expect(STEP_ENSURE_FUNCTIONS[5]).toBe('ensureStep5FinalizationRecordExists');
    });
  });

  describe('Helper Functions', () => {
    const mockSessionData: FullOnboardingSessionData = {
      onboardingSession: { id: 'session-1', current_step: 1 } as any,
      step1_businessProfile: { id: 'step1-id' } as any,
      step2_farmOperations: { id: 'step2-id' } as any,
      step3_financialSystems: { id: 'step3-id' } as any,
      step4_registrationsInsurance: { id: 'step4-id' } as any,
      step5_finalization: { id: 'step5-id' } as any,
      documents: [],
    };

    it('should extract step data correctly for all 5 steps', () => {
      expect(getStepData(mockSessionData, 'step1')?.id).toBe('step1-id');
      expect(getStepData(mockSessionData, 'step2')?.id).toBe('step2-id');
      expect(getStepData(mockSessionData, 'step3')?.id).toBe('step3-id');
      expect(getStepData(mockSessionData, 'step4')?.id).toBe('step4-id');
      expect(getStepData(mockSessionData, 'step5')?.id).toBe('step5-id');
    });

    it('should extract step IDs correctly for all 5 steps', () => {
      expect(getStepId(mockSessionData, 'step1')).toBe('step1-id');
      expect(getStepId(mockSessionData, 'step2')).toBe('step2-id');
      expect(getStepId(mockSessionData, 'step3')).toBe('step3-id');
      expect(getStepId(mockSessionData, 'step4')).toBe('step4-id');
      expect(getStepId(mockSessionData, 'step5')).toBe('step5-id');
    });

    it('should handle null session data gracefully', () => {
      expect(getStepData(null, 'step1')).toBeNull();
      expect(getStepData(null, 'step2')).toBeNull();
      expect(getStepData(null, 'step3')).toBeNull();
      expect(getStepData(null, 'step4')).toBeNull();
      expect(getStepData(null, 'step5')).toBeNull();

      expect(getStepId(null, 'step1')).toBeNull();
      expect(getStepId(null, 'step2')).toBeNull();
      expect(getStepId(null, 'step3')).toBeNull();
      expect(getStepId(null, 'step4')).toBeNull();
      expect(getStepId(null, 'step5')).toBeNull();
    });
  });

  describe('Type Safety Validation', () => {
    it('should maintain type safety for step data access', () => {
      const mockSessionData: FullOnboardingSessionData = {
        onboardingSession: { id: 'session-1', current_step: 1 } as any,
        step1_businessProfile: { 
          id: 'step1-id',
          businessRegistration: { business_name: 'Test Farm' } as any,
          contacts: [],
          addresses: [],
          keyStaff: []
        } as any,
        step2_farmOperations: null,
        step3_financialSystems: null,
        step4_registrationsInsurance: { 
          id: 'step4-id',
          vehicleRegistrations: [],
          insurancePolicies: [],
          licensesNew: []
        } as any,
        step5_finalization: { 
          id: 'step5-id',
          dataMigration: null,
          permissions: [],
          agreements: [],
          payments: null,
          communicationPreferences: null,
          finalizationSubmission: null
        } as any,
        documents: [],
      };

      // Test that step data maintains proper structure
      const step1Data = getStepData(mockSessionData, 'step1');
      expect(step1Data).toBeDefined();
      expect(step1Data?.businessRegistration).toBeDefined();

      const step4Data = getStepData(mockSessionData, 'step4');
      expect(step4Data).toBeDefined();
      expect(Array.isArray(step4Data?.vehicleRegistrations)).toBe(true);
      expect(Array.isArray(step4Data?.insurancePolicies)).toBe(true);
      expect(Array.isArray(step4Data?.licensesNew)).toBe(true);

      const step5Data = getStepData(mockSessionData, 'step5');
      expect(step5Data).toBeDefined();
      expect(Array.isArray(step5Data?.permissions)).toBe(true);
      expect(Array.isArray(step5Data?.agreements)).toBe(true);
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain access to existing step data', () => {
      const mockSessionData: FullOnboardingSessionData = {
        onboardingSession: { id: 'session-1', current_step: 1 } as any,
        step1_businessProfile: { id: 'step1-id' } as any,
        step2_farmOperations: { id: 'step2-id' } as any,
        step3_financialSystems: { id: 'step3-id' } as any,
        step4_registrationsInsurance: { id: 'step4-id' } as any,
        step5_finalization: { id: 'step5-id' } as any,
        documents: [],
        // Legacy field for backward compatibility
        step4_agreements: { id: 'legacy-step4-id' } as any,
      };

      // Ensure existing steps still work
      expect(getStepId(mockSessionData, 'step1')).toBe('step1-id');
      expect(getStepId(mockSessionData, 'step2')).toBe('step2-id');
      expect(getStepId(mockSessionData, 'step3')).toBe('step3-id');
      
      // New steps work correctly
      expect(getStepId(mockSessionData, 'step4')).toBe('step4-id');
      expect(getStepId(mockSessionData, 'step5')).toBe('step5-id');
    });
  });
});
