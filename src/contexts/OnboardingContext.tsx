/* eslint-disable react-refresh/only-export-components */

/**
 * OnboardingContext - 5-Step Infrastructure (Phase 1)
 *
 * UPDATED: Expanded from 4-step to 5-step onboarding system
 *
 * This context provides centralized state management and CRUD operations for the
 * expanded 5-step onboarding wizard:
 *
 * Step 1: Business Profile (business_registration, contacts, addresses, key_staff)
 * Step 2: Farm Operations (activities, licenses, suppliers, contracts, chemical_usage)
 * Step 3: Financial Systems (bookkeeping, payroll, assets)
 * Step 4: Registrations & Insurance (NEW - vehicle_registrations, insurance_policies, licenses_new)
 * Step 5: Finalization (NEW - agreements, permissions, payments, data_migration, communication_preferences, finalization_submission)
 *
 * Key Features:
 * - Generic CRUD functions with type safety (createRecord, updateRecord, deleteRecord, upsertRecord)
 * - Step initialization functions (ensureStep1-5RecordExists)
 * - Hierarchical data management with proper foreign key relationships
 * - Backward compatibility with legacy step4_agreements
 * - Helper functions for data extraction and validation
 *
 * IMPORTANT: Always use the generic CRUD functions instead of direct Supabase calls
 * to ensure proper type safety and error handling.
 */
import React, { createContext, useState, useEffect, ReactNode, useContext, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import type { PostgrestError } from '@supabase/supabase-js';
import {
  FullOnboardingSessionData,
  OnboardingSession,
  BusinessRegistration,
  Contact,
  KeyStaff,
  Address,
  Activity,
  License,
  Supplier,
  Contract as FarmContract,
  ChemicalUsage,
  Asset,
  DataMigration,
  Permission,
  Agreement,
  Payment,
  Bookkeeping,
  Payroll,
  CommunicationPreferences,
  Document,
  AgreementTypeEnum
} from '@/types/onboarding';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/types/database.types';
import { logger } from '@/utils/logger';
import {
  humanizeTableName,
  formatPostgresError,
  getStepData,
  getStepId,
  isSessionDataValid,
  getSessionId,
  getCurrentStep,
  getSessionStatus,
  createErrorHandler,
  isPostgresError,
} from '@/utils/onboarding-context-helpers';
import {
  validateFormField,
  validateForm,
  type ValidationConfig,
} from '@/utils/form-validation';
import {
  formatPhoneNumber,
  formatAbn,
  formatAcn,
  normalizePhoneNumber,
  normalizeBusinessNumber,
} from '@/utils/form-formatters';
import {
  BUSINESS_STRUCTURES,
  CONTACT_TYPES,
  ADDRESS_TYPES,
  ACTIVITY_TYPES,
  LICENSE_TYPES,
  ASSET_CATEGORIES,
  COMMUNICATION_METHODS,
  REPORTING_FREQUENCIES,
  BAS_LODGEMENT_FREQUENCIES,
  VALIDATION_PATTERNS,
  DEFAULT_VALUES,
  FILE_UPLOAD_CONFIGS,
} from '@/utils/business-constants';
import {
  validateABN,
  validateACN,
  validateAustralianPhone,
  validateEmail,
  validateEnumValue,
  validateArrayField,
  validateStep1,
  validateCompleteOnboarding,
  formatFieldName,
} from '@/utils/onboarding-validation';
import {
  initiateSecureUpload,
  finalizeSecureUpload,
  importAssetsCSVApi,
  ImportAssetsCSVResult,
  processDigitalSignature,
  ProcessDigitalSignatureResult,
  invokeEncryptAndStoreSensitiveField,
  EncryptionResult,
  submitOnboardingSession,
  SubmitOnboardingResult,
  loadOrCreateOnboardingSession,
  mirrorToSharePoint,
  MirrorToSharePointResult,
  deleteSecureDocument,
  DeleteDocumentResult,
} from '@/integrations/supabase/api';

// --- Generic Utility Types for 'farms' Schema ---
type FarmsSchema = Database['farms'];
export type TableName = keyof FarmsSchema['Tables'];
export type TableRow<T extends TableName> = FarmsSchema['Tables'][T]['Row'];
export type TableInsert<T extends TableName> = FarmsSchema['Tables'][T]['Insert'];
export type TableUpdate<T extends TableName> = FarmsSchema['Tables'][T]['Update'];

// --- Type exports for external components ---
// All types are now available from @/types/onboarding - use direct imports instead

// --- Specific Database Operation Types ---
export type DataMigrationUpsert = TableInsert<'data_migration'> & { id?: string };
export type AgreementUpsert = TableInsert<'agreements'> & { id?: string };
export type PaymentDetailsUpsert = TableInsert<'payments'> & { id?: string };
export type CommunicationPreferencesUpsert = TableInsert<'communication_preferences'> & { id?: string };

export type OnboardingContextType = {
  sessionData: FullOnboardingSessionData | null;
  loading: boolean;
  currentStep: number;
  error: string | null;
  updateCurrentStep: (step: number) => Promise<void>;
  refreshSessionData: (showToast?: boolean) => Promise<void>;
  sessionId: string | null;
  isEncryptingField: boolean;
  isSubmittingSession: boolean;
  isProcessingSignature: boolean;
  isDeletingDocument: boolean;

  // --- Generic CRUD Functions ---
  createRecord: <T extends TableName>(
    tableName: T,
    data: TableInsert<T> | TableInsert<T>[]
  ) => Promise<TableRow<T>[] | null>;

  updateRecord: <T extends TableName>(
    tableName: T,
    recordId: string,
    data: TableUpdate<T>
  ) => Promise<TableRow<T>[] | null>;

  deleteRecord: <T extends TableName>(
    tableName: T,
    recordId: string
  ) => Promise<boolean>;

  upsertRecord: <T extends TableName>(
    tableName: T,
    data: TableInsert<T> | TableInsert<T>[],
    options?: { onConflict?: string }
  ) => Promise<TableRow<T>[] | null>;

  ensureLinkedRecord: <T extends TableName>(
    params: {
      tableName: T;
      linkCondition: Partial<TableRow<T>>;
      defaultValues: Partial<TableInsert<T>>;
      existingRecordFromSession?: TableRow<T> | null;
    }
  ) => Promise<TableRow<T> | null>;

  // --- Step Parent Record Ensure Functions (Orchestrators) ---
  ensureStep1BusinessProfileRecordExists: (onboardingSessionId: string) => Promise<TableRow<'step_1_business_profile'> | null>;
  ensureStep2FarmOperationsRecordExists: (onboardingSessionId: string) => Promise<TableRow<'step_2_farm_operations'> | null>;
  ensureStep3FinancialSystemsRecordExists: (onboardingSessionId: string) => Promise<TableRow<'step_3_financial_systems'> | null>;
  ensureStep4RegistrationsInsuranceRecordExists: (onboardingSessionId: string) => Promise<TableRow<'step_4_registrations_insurance'> | null>;
  ensureStep5FinalizationRecordExists: (onboardingSessionId: string) => Promise<TableRow<'step_5_finalization'> | null>;

  // --- DEPRECATED: Legacy Step 4 Agreements (Backward Compatibility) ---
  ensureStep4AgreementsRecordExists: (onboardingSessionId: string) => Promise<TableRow<'step_4_agreements'> | null>;

  // --- Step 4 Child Record Ensure Functions ---
  ensureDataMigrationRecordExists: (step4Id: string) => Promise<TableRow<'data_migration'> | null>;
  ensureCommunicationPreferencesRecordExists: (step4Id: string) => Promise<TableRow<'communication_preferences'> | null>;
  ensurePaymentRecordExists: (step4Id: string) => Promise<TableRow<'payments'> | null>;

  // --- Edge Function Invokers (Remain Specific) ---
  invokeImportAssetsCSV: (step3Id: string, file: File) => Promise<ImportAssetsCSVResult>;
  invokeProcessDigitalSignature: (agreementType: AgreementTypeEnum, signatureDataUrl: string) => Promise<ProcessDigitalSignatureResult>;

  uploadAndFinalizeDocument: (
    file: File,
    relatedToEntity: string, // Consider making this typed if possible: keyof FullOnboardingSessionData or specific entity names
    relatedToId: string,
  ) => Promise<{ success: boolean; documentId?: string; error?: string; step?: 'initiate' | 'upload' | 'finalize' }>;

  deleteDocument: (documentId: string) => Promise<{ success: boolean; error?: string }>;

  encryptAndStoreSensitiveField: (
    params: {
      tableName: 'bookkeeping' | 'payroll' | 'payments';
      fieldName: 'access_credentials' | 'encrypted_access_credentials' | 'bank_account_details';
      plainTextValue: string;
    }
  ) => Promise<EncryptionResult>;

  submitOnboarding: () => Promise<SubmitOnboardingResult>;

  // --- Kept Specific Functions (Can be refactored to use generics internally if desired later) ---
  setPermissions: (permissions: TableInsert<'permissions'>[], step4Id: string) => Promise<boolean>;
  getSignedDocumentUrl: (storagePath: string) => Promise<string | null>;

  // --- Helper Functions for External Components ---
  // Make helper functions available to consuming components
  helpers: {
    getStepData: typeof getStepData;
    getStepId: typeof getStepId;
    isSessionDataValid: typeof isSessionDataValid;
    getSessionId: typeof getSessionId;
    getCurrentStep: typeof getCurrentStep;
    getSessionStatus: typeof getSessionStatus;
    formatPhoneNumber: typeof formatPhoneNumber;
    formatAbn: typeof formatAbn;
    formatAcn: typeof formatAcn;
    validateABN: typeof validateABN;
    validateACN: typeof validateACN;
    validateEmail: typeof validateEmail;
    validateAustralianPhone: typeof validateAustralianPhone;
    humanizeTableName: typeof humanizeTableName;
    formatFieldName: typeof formatFieldName;
  };
};

export const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

interface OnboardingProviderProps {
  children: ReactNode;
}

// Enhanced error tracking types for file upload operations
interface FileUploadError {
  step: 'initiate' | 'upload' | 'finalize';
  fileName?: string;
  fileSize?: number;
  relatedToEntity?: string;
  relatedToId?: string;
  message: string;
  timestamp: string;
  operation: string;
}

/*
 * AVAILABLE HELPER FUNCTIONS FOR FUTURE USE:
 * 
 * From @/utils/business-constants.ts:
 * - BUSINESS_STRUCTURES, CONTACT_TYPES, ADDRESS_TYPES, ACTIVITY_TYPES
 * - LICENSE_TYPES, ASSET_CATEGORIES, COMMUNICATION_METHODS
 * - VALIDATION_PATTERNS, DEFAULT_VALUES, FILE_UPLOAD_CONFIGS
 * 
 * From @/utils/form-formatters.ts:
 * - formatPhoneNumber(), formatAbn(), formatAcn(), formatCurrency()
 * - formatDateForInput(), formatDateForDisplay()
 * - normalizePhoneNumber(), normalizeBusinessNumber()
 * 
 * From @/utils/onboarding-validation.ts:
 * - validateABN(), validateACN(), validateAustralianPhone(), validateEmail()
 * - validateStep1(), validateCompleteOnboarding(), formatFieldName()
 * 
 * From @/utils/onboarding-context-helpers.ts:
 * - getStepData(), getStepId(), isStepInitialized()
 * - getStepChildRecords(), hasRecord(), countRecords()
 * - All already integrated: formatPostgresError, humanizeTableName, etc.
 */

/**
 * Provides the `OnboardingContext` to child components.
 * This component is responsible for creating or loading the user's
 * onboarding session and exposes generic CRUD helpers used throughout
 * the wizard. It must wrap all pages that require onboarding data.
 */
export const OnboardingProvider = ({ children }: OnboardingProviderProps) => {
  const { user, loading: authLoading } = useAuth();
  const { toast } = useToast();

  const [sessionData, setSessionData] = useState<FullOnboardingSessionData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isEncryptingField, setIsEncryptingField] = useState<boolean>(false);
  const [isSubmittingSession, setIsSubmittingSession] = useState<boolean>(false);
  const [isProcessingSignature, setIsProcessingSignature] = useState<boolean>(false);
  const [isDeletingDocument, setIsDeletingDocument] = useState<boolean>(false);
  const currentStep = getCurrentStep(sessionData);

  // Enhanced session context for error logging
  const getSessionContext = useCallback(() => ({
    userId: user?.id,
    sessionId: getSessionId(sessionData),
    currentStep,
    hasSessionData: isSessionDataValid(sessionData),
    authLoading,
    loading,
  }), [user?.id, sessionData, currentStep, authLoading, loading]);

  /**
   * Fetches the latest onboarding session data from Supabase and updates
   * `sessionData`. Optionally shows a toast on success.
   */
  const refreshSessionData = useCallback(async (showToast = false) => {
    if (!user) return;
    try {
      const { data, error: rpcError } = await supabase
        .schema('public')
        .rpc('get_onboarding_session_data');

      if (rpcError) throw rpcError;
      if (data) {
        setSessionData(data as unknown as FullOnboardingSessionData);
        if (showToast) {
          toast({ title: "Data Refreshed", description: "Latest data loaded from server." });
        }
      }
    } catch (e: unknown) {
      logger.error('Error refreshing session data:', e);
      setError('Failed to refresh session data.');
      toast({ title: "Error", description: "Could not refresh your session data.", variant: "destructive" });
    }
  }, [user, toast]);

  // --- Helper Functions ---
  /**
   * Determines if a data update is a field-level update (single field change)
   * vs a full entity operation (multiple fields or new entity)
   */
  const isFieldLevelUpdate = <T extends TableInsert<TableName> | TableUpdate<TableName>>(
    data: T | T[]
  ): boolean => {
    if (Array.isArray(data)) return false; // Multiple records = not field update

    const dataObj = data as Record<string, unknown>;
    const fieldCount = Object.keys(dataObj).filter(key =>
      key !== 'id' &&
      key !== 'created_at' &&
      key !== 'updated_at' &&
      dataObj[key] !== undefined
    ).length;

    // Single field update (excluding metadata fields)
    return fieldCount === 1;
  };

  // --- Generic CRUD Function Implementations ---
  /**
   * Inserts one or more records into the specified table and refreshes session data.
   */
  const createRecord = async <T extends TableName>(tableName: T, data: TableInsert<T> | TableInsert<T>[]): Promise<TableRow<T>[] | null> => {
    const operation = `createRecord:${String(tableName)}`;
    const context = {
      ...getSessionContext(),
      tableName: String(tableName),
      isArray: Array.isArray(data),
      recordCount: Array.isArray(data) ? data.length : 1,
    };

    logger.info(`[${operation}] Starting record creation`, context);

    try {
      const { data: insertedData, error: insertError } = await supabase
        .schema('farms')
        .from(tableName)
        .insert(data)
        .select();

      if (insertError) {
        logger.error(`[${operation}] Insert failed:`, {
          ...context,
          error: formatPostgresError(insertError),
          insertData: data,
        });
        throw insertError;
      }

      if (!insertedData) {
        throw new Error('No data returned from insert operation');
      }

      logger.info(`[${operation}] Record creation successful`, {
        ...context,
        insertedCount: insertedData.length,
      });

      // Always refresh session data to ensure UI consistency
      // For field-level updates, refresh without toast to avoid noise
      if (isFieldLevelUpdate(data)) {
        await refreshSessionData(false); // Silent refresh for field updates
      } else {
        await refreshSessionData(true);
        toast({
          title: "Record Created",
          description: `${humanizeTableName(String(tableName))} record(s) added successfully.`
        });
      }

      return insertedData as TableRow<T>[];

    } catch (e: unknown) {
      const errorMessage = formatPostgresError(e);
      logger.error(`[${operation}] Operation failed:`, {
        ...context,
        error: errorMessage,
      });

      toast({
        title: "Save Error",
        description: `Failed to add ${humanizeTableName(String(tableName))} record(s): ${errorMessage}`,
        variant: "destructive"
      });

      return null;
    }
  };

  /**
   * Updates a record by ID in the specified table.
   */
  const updateRecord = async <T extends TableName>(tableName: T, recordId: string, data: TableUpdate<T>): Promise<TableRow<T>[] | null> => {
    const operation = `updateRecord:${String(tableName)}`;
    const context = {
      ...getSessionContext(),
      tableName: String(tableName),
      recordId,
    };

    logger.info(`[${operation}] Starting record update`, context);

    try {
      const { data: updatedData, error: updateError } = await supabase
        .schema('farms')
        .from(tableName)
        .update(data)
        .eq('id', recordId)
        .select();

      if (updateError) {
        logger.error(`[${operation}] Update failed:`, {
          ...context,
          error: formatPostgresError(updateError),
          updateData: data,
        });
        throw updateError;
      }

      if (!updatedData) {
        throw new Error('No data returned from update operation');
      }

      logger.info(`[${operation}] Record update successful`, {
        ...context,
        updatedCount: updatedData.length,
      });

      // Always refresh session data to ensure UI consistency
      // For field-level updates, refresh without toast to avoid noise
      if (isFieldLevelUpdate(data)) {
        await refreshSessionData(false); // Silent refresh for field updates
      } else {
        await refreshSessionData(true);
        toast({
          title: "Record Updated",
          description: `${humanizeTableName(String(tableName))} details updated successfully.`
        });
      }

      return updatedData as TableRow<T>[];

    } catch (e: unknown) {
      const errorMessage = formatPostgresError(e);
      logger.error(`[${operation}] Operation failed:`, {
        ...context,
        error: errorMessage,
      });

      toast({
        title: "Save Error",
        description: `Failed to save ${humanizeTableName(String(tableName))} details: ${errorMessage}`,
        variant: "destructive"
      });

      return null;
    }
  };

  /**
   * Deletes a record by ID from the specified table.
   */
  const deleteRecord = async <T extends TableName>(tableName: T, recordId: string): Promise<boolean> => {
    const operation = `deleteRecord:${String(tableName)}`;
    const context = {
      ...getSessionContext(),
      tableName: String(tableName),
      recordId,
    };

    logger.info(`[${operation}] Starting record deletion`, context);

    try {
      const { error } = await supabase
        .schema('farms')
        .from(tableName)
        .delete()
        .eq('id', recordId);

      if (error) throw error;
      await refreshSessionData(true);
      toast({
        title: "Record Deleted",
        description: `${humanizeTableName(String(tableName))} record removed successfully.`
      });
      return true;
    } catch (e: unknown) {
      const errorMessage = formatPostgresError(e);
      logger.error(`[${operation}] Operation failed:`, {
        ...context,
        error: errorMessage,
      });

      toast({
        title: "Delete Error",
        description: `Failed to delete ${humanizeTableName(String(tableName))} record: ${errorMessage}`,
        variant: "destructive"
      });

      return false;
    }
  };

  /**
   * Inserts or updates a record using Supabase `upsert`.
   */
  const upsertRecord = async <T extends TableName>(
    tableName: T,
    data: TableInsert<T> | TableInsert<T>[],
    options?: { onConflict?: string }
  ): Promise<TableRow<T>[] | null> => {
    const operation = `upsertRecord:${String(tableName)}`;
    const context = {
      ...getSessionContext(),
      tableName: String(tableName),
      isArray: Array.isArray(data),
      recordCount: Array.isArray(data) ? data.length : 1,
    };

    logger.info(`[${operation}] Starting record upsert`, context);

    try {
      const { data: upsertedData, error: upsertError } = await supabase
        .schema('farms')
        .from(tableName)
        .upsert(data, options)
        .select();

      if (upsertError) {
        logger.error(`[${operation}] Upsert failed:`, {
          ...context,
          error: formatPostgresError(upsertError),
          upsertData: data,
        });
        throw upsertError;
      }

      if (!upsertedData) {
        throw new Error('No data returned from upsert operation');
      }

      logger.info(`[${operation}] Record upsert successful`, {
        ...context,
        upsertedCount: upsertedData.length,
      });

      await refreshSessionData(true);
      toast({
        title: "Record Saved",
        description: `${humanizeTableName(String(tableName))} record(s) saved successfully.`
      });

      return upsertedData as TableRow<T>[];

    } catch (e: unknown) {
      const errorMessage = formatPostgresError(e);
      logger.error(`[${operation}] Operation failed:`, {
        ...context,
        error: errorMessage,
      });

      toast({
        title: "Save Error",
        description: `Failed to save ${humanizeTableName(String(tableName))} record(s): ${errorMessage}`,
        variant: "destructive"
      });

      return null;
    }
  };

  /**
   * Ensures a record exists that matches the given link condition. If it does
   * not exist the record is created using the provided default values.
   */
  const ensureLinkedRecord = useCallback(async <T extends TableName>(
    params: {
      tableName: T;
      linkCondition: Partial<TableRow<T>>;
      defaultValues: Partial<TableInsert<T>>;
      existingRecordFromSession?: TableRow<T> | null;
    }
  ): Promise<TableRow<T> | null> => {
    const { tableName, linkCondition, defaultValues, existingRecordFromSession } = params;
    const operation = `ensureLinkedRecord:${String(tableName)}`;
    const context = {
      ...getSessionContext(),
      tableName: String(tableName),
      linkCondition: JSON.stringify(linkCondition),
      defaultValues: JSON.stringify(defaultValues),
      existingRecordFromSession: existingRecordFromSession?.id ?? 'N/A',
    };

    logger.info(`[${operation}] Starting ensureLinkedRecord`, context);

    if (existingRecordFromSession?.id) {
      logger.info(`[${operation}] Record found in session data (ID: ${existingRecordFromSession.id}). Returning existing.`);
      return existingRecordFromSession;
    }

    for (const key in linkCondition) {
      if (linkCondition[key as keyof typeof linkCondition] === undefined) {
        logger.warn(`[${operation}] LinkCondition field '${key}' is undefined. This might lead to unexpected behavior or inability to find/create the record correctly.`);
      }
    }

    try {
      const baseQuery = supabase.schema('farms').from(tableName);
      const definedLinkCondition = Object.entries(linkCondition).reduce((acc, [k, v]) => {
        if (v !== undefined) {
          (acc as Partial<TableRow<T>>)[k as keyof TableRow<T>] = v as TableRow<T>[keyof TableRow<T>];
        }
        return acc;
      }, {} as Partial<TableRow<T>>);

      let queryChain;
      if (Object.keys(definedLinkCondition).length > 0) {
        logger.info(`[${operation}] Attempting to select existing record from DB with condition (stringified):`, JSON.stringify(definedLinkCondition));

        queryChain = baseQuery.select();
        for (const [key, value] of Object.entries(definedLinkCondition)) {
          queryChain = queryChain.eq(key, value);
        }
      } else {
        logger.warn(`[${operation}] No valid link conditions provided to select existing record.`);
        queryChain = baseQuery.select();
      }

      const { data: existingDbRecords, error: selectError } = await queryChain.limit(1);

      if (selectError) {
        logger.error(`[${operation}] Error selecting existing record:`, selectError.message, JSON.stringify(selectError));
        throw selectError;
      }

      if (existingDbRecords && existingDbRecords.length > 0) {
        logger.info(`[${operation}] Found existing record in DB (ID: ${existingDbRecords[0].id}). Returning it.`);
        return existingDbRecords[0] as TableRow<T>;
      }

      logger.info(`[${operation}] Record not found in DB. Attempting to create with defaults (stringified):`, JSON.stringify(defaultValues), 'and link conditions (stringified):', JSON.stringify(linkCondition));
      const insertPayload = { ...defaultValues, ...linkCondition } as TableInsert<T>;
      logger.info(`[${operation}] Insert payload (stringified):`, JSON.stringify(insertPayload));

      const { data: createdRecord, error: insertError } = await supabase
        .schema('farms')
        .from(tableName)
        .insert(insertPayload)
        .select()
        .single();

      if (insertError) {
        logger.error(`[${operation}] Error inserting new record:`, insertError.message, JSON.stringify(insertError));
        if (insertError.code === '23505') {
          logger.warn(`[${operation}] Unique constraint violation (code 23505). Re-fetching. Condition (stringified):`, JSON.stringify(definedLinkCondition));
          const refetchBaseQuery = supabase.schema('farms').from(tableName);
          let refetchQueryChain;
          if (Object.keys(definedLinkCondition).length > 0) {
            refetchQueryChain = refetchBaseQuery.select();
            for (const [key, value] of Object.entries(definedLinkCondition)) {
              refetchQueryChain = refetchQueryChain.eq(key, value);
            }
          } else {
            refetchQueryChain = refetchBaseQuery.select();
          }
          const { data: raceExistingRecords, error: raceSelectError } = await refetchQueryChain.limit(1);

          if (raceSelectError) {
            logger.error(`[${operation}] Error re-fetching after unique constraint violation:`, raceSelectError.message, JSON.stringify(raceSelectError));
            throw raceSelectError;
          }
          if (raceExistingRecords && raceExistingRecords.length > 0) {
            logger.info(`[${operation}] Successfully re-fetched record (ID: ${raceExistingRecords[0].id}) after unique constraint violation.`);
            await refreshSessionData(true);
            return raceExistingRecords[0] as TableRow<T>;
          }
          logger.warn(`[${operation}] Re-fetch after unique constraint violation did not find a record.`);
        }
        throw insertError;
      }

      if (!createdRecord) {
        logger.error(`[${operation}] Record creation failed (createdRecord is null/undefined after insert).`);
        throw new Error(`Failed to create ${humanizeTableName(String(tableName))} record.`);
      }

      logger.info(`[${operation}] Successfully created record (ID: ${createdRecord.id}). Refreshing session data.`);
      await refreshSessionData(true);
      logger.info(`[${operation}] ensureLinkedRecord completed successfully for ID: ${createdRecord.id}`);
      return createdRecord as TableRow<T>;

    } catch (e: unknown) {
      const errorMessage = formatPostgresError(e);
      logger.error(`[${operation}] Operation failed:`, {
        ...context,
        error: errorMessage,
      });

      toast({
        title: "Initialization Error",
        description: `Could not ensure ${humanizeTableName(String(tableName))} record exists: ${errorMessage}`,
        variant: "destructive"
      });

      return null;
    }
  }, [refreshSessionData, toast, getSessionContext]);


  // --- Step Parent Record Ensure Functions (Refactored) ---
  /**
   * Helper used by step-specific ensure functions to guarantee the existence
   * of the parent record for a given onboarding step.
   */
  const ensureStepParentRecord = useCallback(async <StepTableName extends 'step_1_business_profile' | 'step_2_farm_operations' | 'step_3_financial_systems' | 'step_4_registrations_insurance' | 'step_5_finalization' | 'step_4_agreements'>(
    stepTableName: StepTableName,
    onboardingSessionId: string,
    existingStepRecordInSession: TableRow<StepTableName> | null | undefined
  ): Promise<TableRow<StepTableName> | null> => {
    if (!onboardingSessionId) {
      logger.warn(`[ensureStepParentRecord] onboardingSessionId is required for ${stepTableName}.`);
      return null;
    }
    return ensureLinkedRecord({
      tableName: stepTableName,
      linkCondition: { onboarding_session_id: onboardingSessionId } as Partial<TableRow<StepTableName>>,
      defaultValues: { onboarding_session_id: onboardingSessionId } as Partial<TableInsert<StepTableName>>,
      existingRecordFromSession: existingStepRecordInSession,
    });
  }, [ensureLinkedRecord]);

  const ensureStep1BusinessProfileRecordExists = useCallback(
    (onboardingSessionId: string) => {
      // Always check database fresh to avoid dependency cycles
      // ensureLinkedRecord will handle duplicate prevention
      return ensureStepParentRecord('step_1_business_profile', onboardingSessionId, null);
    },
    [ensureStepParentRecord]
  );
  const ensureStep2FarmOperationsRecordExists = useCallback(
    (onboardingSessionId: string) => {
      // Always check database fresh to avoid dependency cycles
      // ensureLinkedRecord will handle duplicate prevention
      return ensureStepParentRecord('step_2_farm_operations', onboardingSessionId, null);
    },
    [ensureStepParentRecord]
  );
  const ensureStep3FinancialSystemsRecordExists = useCallback(
    (onboardingSessionId: string) => {
      // Always check database fresh to avoid dependency cycles
      // ensureLinkedRecord will handle duplicate prevention
      return ensureStepParentRecord('step_3_financial_systems', onboardingSessionId, null);
    },
    [ensureStepParentRecord]
  );
  const ensureStep4RegistrationsInsuranceRecordExists = useCallback(
    (onboardingSessionId: string) => {
      // Always check database fresh to avoid dependency cycles
      // ensureLinkedRecord will handle duplicate prevention
      return ensureStepParentRecord('step_4_registrations_insurance', onboardingSessionId, null);
    },
    [ensureStepParentRecord]
  );

  const ensureStep5FinalizationRecordExists = useCallback(
    (onboardingSessionId: string) => {
      // Always check database fresh to avoid dependency cycles
      // ensureLinkedRecord will handle duplicate prevention
      return ensureStepParentRecord('step_5_finalization', onboardingSessionId, null);
    },
    [ensureStepParentRecord]
  );

  // --- DEPRECATED: Legacy Step 4 Agreements (Backward Compatibility) ---
  const ensureStep4AgreementsRecordExists = useCallback(
    (onboardingSessionId: string) => {
      // Always check database fresh to avoid dependency cycles
      // ensureLinkedRecord will handle duplicate prevention
      return ensureStepParentRecord('step_4_agreements', onboardingSessionId, null);
    },
    [ensureStepParentRecord]
  );

  // --- Step 4 Child Record Ensure Function Implementations ---
  const ensureDataMigrationRecordExists = useCallback(async (step4Id: string): Promise<TableRow<'data_migration'> | null> => {
    if (!step4Id) {
      logger.warn('[ensureDataMigrationRecordExists] step4Id is required.');
      return null;
    }
    return ensureLinkedRecord({
      tableName: 'data_migration',
      linkCondition: { step_4_id: step4Id },
      defaultValues: { step_4_id: step4Id },
      existingRecordFromSession: null, // Always check database fresh to avoid dependency cycles
    });
  }, [ensureLinkedRecord]);

  const ensureCommunicationPreferencesRecordExists = useCallback(async (step4Id: string): Promise<TableRow<'communication_preferences'> | null> => {
    if (!step4Id) {
      logger.warn('[ensureCommunicationPreferencesRecordExists] step4Id is required.');
      return null;
    }
    return ensureLinkedRecord({
      tableName: 'communication_preferences',
      linkCondition: { step_4_id: step4Id },
      defaultValues: { step_4_id: step4Id, preferred_methods: [] },
      existingRecordFromSession: null, // Always check database fresh to avoid dependency cycles
    });
  }, [ensureLinkedRecord]);

  const ensurePaymentRecordExists = useCallback(async (step4Id: string): Promise<TableRow<'payments'> | null> => {
    if (!step4Id) {
      logger.warn('[ensurePaymentRecordExists] step4Id is required.');
      return null;
    }
    return ensureLinkedRecord({
      tableName: 'payments',
      linkCondition: { step_4_id: step4Id },
      defaultValues: { step_4_id: step4Id },
      existingRecordFromSession: null, // Always check database fresh to avoid dependency cycles
    });
  }, [ensureLinkedRecord]);

  /**
   * Attempts to load the user's existing onboarding session or creates a new
   * one if none is found using the load-or-create-onboarding-session Edge Function.
   */
  const loadOrCreateSession = useCallback(async () => {
    if (!user) {
      setSessionData(null);
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);

    try {
      // Use the Edge Function instead of direct database access
      const fullData = await loadOrCreateOnboardingSession();
      setSessionData(fullData);

      // Show welcome message if this is a newly created session
      if (fullData.onboardingSession?.current_step === 1 &&
        !fullData.step1_businessProfile?.businessRegistration) {
        toast({ title: "Welcome!", description: "Your onboarding session has started." });
      }
    } catch (e: unknown) {
      const errorHandler = createErrorHandler('loadOrCreateSession', {
        authLoading,
        userId: user?.id,
        hasUser: !!user,
      });

      const errorResult = errorHandler(e);
      const errorMessage = errorResult.error;

      setError(errorMessage);
      toast({ title: "Error", description: errorMessage, variant: "destructive" });
    } finally {
      setLoading(false);
    }
  }, [user, toast, setError, authLoading]);

  useEffect(() => {
    if (!authLoading) {
      loadOrCreateSession();
    }
  }, [user, authLoading, loadOrCreateSession]);

  /**
   * Persists the user's current step in the onboarding process to the backend.
   */
  const updateCurrentStep = async (step: number) => {
    const sessionId = getSessionId(sessionData);
    if (!sessionId || !isSessionDataValid(sessionData)) return;

    const { error: updateError } = await supabase
      .schema('farms')
      .from('onboarding_sessions')
      .update({ current_step: step })
      .eq('id', sessionId);

    if (updateError) {
      logger.error('Error updating current step:', updateError);
      toast({ title: "Error", description: "Failed to update your progress.", variant: "destructive" });
      setError('Failed to update step.');
    } else {
      setSessionData(prev => prev ? { ...prev, onboardingSession: { ...prev.onboardingSession!, current_step: step } } : null);
    }
  };

  // --- Specific Function Implementations (Kept or to be refactored) ---
  /**
   * Wrapper for the `import-asset-registry-from-csv` Edge Function.
   *
   * @param step3Id - Foreign key to `step_3_financial_systems`.
   * @param file - CSV file containing asset data.
   */
  const invokeImportAssetsCSV = async (step3Id: string, file: File): Promise<ImportAssetsCSVResult> => {
    if (!step3Id) {
      toast({ title: "Prerequisite Missing", description: "Financial systems profile (Step 3) must be initiated.", variant: "default" });
      return { success: false, error: "Step 3 ID is missing." };
    }
    setLoading(true);
    try {
      const result = await importAssetsCSVApi(file);
      if (result.success) {
        toast({ title: "Success", description: `${result.importedCount || 0} assets imported. ${result.errorCount || 0} errors.` });
        await refreshSessionData(true);
      } else {
        toast({ title: "CSV Import Failed", description: result.message || result.error || 'Unknown import error', variant: "destructive" });
      }
      return result;
    } catch (e: unknown) {
      const msg = e instanceof Error ? e.message : String(e);
      toast({ title: "Error", description: msg, variant: "destructive" });
      return { success: false, error: msg, errors: [msg] };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Calls the `process-digital-signature-and-consent` Edge Function to store a
   * user's signature for a given agreement type.
   */
  const invokeProcessDigitalSignature = async (agreementType: AgreementTypeEnum, signatureDataUrl: string): Promise<ProcessDigitalSignatureResult> => {
    const sessionId = getSessionId(sessionData);
    if (!sessionId || !isSessionDataValid(sessionData)) {
      toast({ title: "Error", description: "Session ID is missing. Cannot process signature.", variant: "destructive" });
      return { success: false, error: "Session ID is missing" };
    }
    setIsProcessingSignature(true);
    try {
      const result = await processDigitalSignature(signatureDataUrl, sessionId, agreementType);
      if (result.success) {
        toast({ title: "Signature Processed", description: result.message || "Your signature has been successfully recorded." });
        await refreshSessionData(false); // Refresh without generic toast
      } else {
        toast({ title: "Signature Failed", description: result.error || result.message || "Failed to process signature.", variant: "destructive" });
      }
      return result;
    } catch (e: unknown) {
      const msg = e instanceof Error ? e.message : String(e);
      logger.error('Error invoking processDigitalSignature:', e);
      toast({ title: "Signature Error", description: msg, variant: "destructive" });
      return { success: false, error: msg };
    } finally {
      setIsProcessingSignature(false);
    }
  };

  /**
   * Enhanced file upload with comprehensive error handling and detailed logging
   */
  const uploadAndFinalizeDocument = async (
    file: File,
    relatedToEntity: string,
    relatedToId: string,
  ): Promise<{ success: boolean; documentId?: string; error?: string; step?: 'initiate' | 'upload' | 'finalize' }> => {
    const operation = 'uploadAndFinalizeDocument';
    const baseContext = {
      ...getSessionContext(),
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      relatedToEntity,
      relatedToId,
    };

    logger.info(`[${operation}] Starting file upload`, baseContext);

    // Early validation
    const sessionId = getSessionId(sessionData);
    if (!sessionId || !isSessionDataValid(sessionData)) {
      const errorMsg = "Session ID is missing. Cannot upload file.";
      logger.error(`[${operation}] Validation failed:`, {
        ...baseContext,
        step: 'validation',
        error: errorMsg,
      });

      toast({
        title: "Upload Error",
        description: errorMsg,
        variant: "destructive"
      });

      return {
        success: false,
        error: errorMsg,
        step: 'initiate'
      };
    }

    if (!file || !relatedToEntity || !relatedToId) {
      const errorMsg = "Missing required parameters for file upload.";
      logger.error(`[${operation}] Validation failed:`, {
        ...baseContext,
        step: 'validation',
        error: errorMsg,
        missingFile: !file,
        missingEntity: !relatedToEntity,
        missingId: !relatedToId,
      });

      toast({
        title: "Upload Error",
        description: errorMsg,
        variant: "destructive"
      });

      return {
        success: false,
        error: errorMsg,
        step: 'initiate'
      };
    }

    setLoading(true);
    let currentStep: 'initiate' | 'upload' | 'finalize' = 'initiate';
    let filePath = '';
    let signedUrl = '';

    try {
      // Step 1: Initiate secure upload
      logger.info(`[${operation}] Step 1: Initiating secure upload`, baseContext);
      currentStep = 'initiate';

      const initiatePayload = {
        sessionId,
        fileName: file.name,
        relatedToEntity,
        relatedToId,
      };

      logger.info(`[${operation}] Initiate payload:`, initiatePayload);

      const initiateResult = await initiateSecureUpload(initiatePayload);

      filePath = initiateResult.filePath;
      signedUrl = initiateResult.signedUrl;

      logger.info(`[${operation}] Initiate successful`, {
        ...baseContext,
        filePath,
        signedUrlLength: signedUrl.length,
        step: 'initiate',
      });

      // Step 2: Upload file to storage
      logger.info(`[${operation}] Step 2: Uploading to storage`, {
        ...baseContext,
        filePath,
        step: 'upload',
      });
      currentStep = 'upload';

      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (!uploadResponse.ok) {
        const responseText = await uploadResponse.text().catch(() => 'Unable to read response text');
        const uploadError = new Error(`Storage upload failed: HTTP ${uploadResponse.status} ${uploadResponse.statusText}. Response: ${responseText}`);

        logger.error(`[${operation}] Upload failed:`, {
          ...baseContext,
          step: 'upload',
          error: formatPostgresError(uploadError),
          responseStatus: uploadResponse.status,
          responseStatusText: uploadResponse.statusText,
          responseText,
          signedUrlValid: !!signedUrl,
          filePath,
        });

        throw uploadError;
      }

      logger.info(`[${operation}] Upload to storage successful`, {
        ...baseContext,
        step: 'upload',
        responseStatus: uploadResponse.status,
        filePath,
      });

      // Step 3: Finalize upload (create database record)
      logger.info(`[${operation}] Step 3: Finalizing upload`, {
        ...baseContext,
        step: 'finalize',
        filePath,
      });
      currentStep = 'finalize';

      const finalizePayload = {
        sessionId,
        documentName: file.name,
        relatedToEntity,
        relatedToId,
        filePath,
      };

      logger.info(`[${operation}] Finalize payload:`, finalizePayload);

      const finalizeResult = await finalizeSecureUpload(finalizePayload);

      if (!finalizeResult.success) {
        const finalizeError = new Error(finalizeResult.error || finalizeResult.message || "Finalization failed without specific error");

        logger.error(`[${operation}] Finalize failed:`, {
          ...baseContext,
          step: 'finalize',
          error: formatPostgresError(finalizeError),
          finalizeResult,
          filePath,
        });

        throw finalizeError;
      }

      if (!finalizeResult.documentId) {
        const finalizeError = new Error("Finalization succeeded but no document ID returned");

        logger.error(`[${operation}] Finalize failed:`, {
          ...baseContext,
          step: 'finalize',
          error: formatPostgresError(finalizeError),
          finalizeResult,
          filePath,
        });

        throw finalizeError;
      }

      logger.info(`[${operation}] Upload completed successfully`, {
        ...baseContext,
        documentId: finalizeResult.documentId,
        filePath,
      });

      // Show initial success message
      toast({
        title: "Upload Successful",
        description: `${file.name} uploaded successfully. Mirroring to SharePoint...`
      });

      await refreshSessionData(true);

      // Step 4: Mirror to SharePoint
      logger.info(`[${operation}] Step 4: Mirroring to SharePoint`, {
        ...baseContext,
        step: 'mirror',
        filePath,
      });

      try {
        const mirrorResult = await mirrorToSharePoint({
          supabaseFilePath: filePath,
          sessionId,
          relatedToEntity,
          relatedToId,
          originalFileName: file.name,
        });

        if (mirrorResult.success) {
          logger.info(`[${operation}] SharePoint mirroring successful`, {
            ...baseContext,
            sharepointUrl: mirrorResult.sharepointUrl,
          });

          toast({
            title: "File Mirrored",
            description: `${file.name} successfully mirrored to SharePoint.`
          });
        } else {
          logger.warn(`[${operation}] SharePoint mirroring failed`, {
            ...baseContext,
            error: mirrorResult.error,
          });

          toast({
            title: "Mirroring Warning",
            description: `File uploaded but SharePoint mirroring failed: ${mirrorResult.error}`,
            variant: "destructive"
          });
        }
      } catch (mirrorError) {
        logger.error(`[${operation}] SharePoint mirroring error`, {
          ...baseContext,
          error: formatPostgresError(mirrorError),
        });

        toast({
          title: "Mirroring Warning",
          description: `File uploaded but SharePoint mirroring encountered an error: ${formatPostgresError(mirrorError)}`,
          variant: "destructive"
        });
      }

      return {
        success: true,
        documentId: finalizeResult.documentId
      };

    } catch (e: unknown) {
      const errorMessage = formatPostgresError(e);
      logger.error(`[${operation}] Operation failed:`, {
        ...baseContext,
        step: currentStep,
        error: errorMessage,
        filePath,
        signedUrlExists: !!signedUrl,
        fileName: file.name,
        fileSize: file.size,
        relatedToEntity,
        relatedToId,
      });

      // Create user-friendly error message based on step
      let userErrorMessage = errorMessage;
      switch (currentStep) {
        case 'initiate':
          userErrorMessage = `Failed to prepare upload: ${errorMessage}`;
          break;
        case 'upload':
          userErrorMessage = `Failed to upload file to storage: ${errorMessage}`;
          break;
        case 'finalize':
          userErrorMessage = `File uploaded but failed to save record: ${errorMessage}`;
          break;
      }

      toast({
        title: "Upload Error",
        description: userErrorMessage,
        variant: "destructive",
        duration: 8000, // Longer duration for upload errors
      });

      return {
        success: false,
        error: errorMessage,
        step: currentStep
      };

    } finally {
      setLoading(false);
    }
  };

  /**
   * Deletes a document securely, removing both the database record and storage file
   */
  const deleteDocument = async (documentId: string): Promise<{ success: boolean; error?: string }> => {
    const operation = 'deleteDocument';
    const baseContext = {
      ...getSessionContext(),
      documentId,
    };

    logger.info(`[${operation}] Starting document deletion`, baseContext);

    // Early validation
    const sessionId = getSessionId(sessionData);
    if (!sessionId || !isSessionDataValid(sessionData)) {
      const errorMsg = "Session ID is missing. Cannot delete document.";
      logger.error(`[${operation}] Validation failed:`, {
        ...baseContext,
        error: errorMsg,
      });

      toast({
        title: "Delete Error",
        description: errorMsg,
        variant: "destructive"
      });

      return {
        success: false,
        error: errorMsg
      };
    }

    if (!documentId) {
      const errorMsg = "Document ID is required for deletion.";
      logger.error(`[${operation}] Validation failed:`, {
        ...baseContext,
        error: errorMsg,
      });

      toast({
        title: "Delete Error",
        description: errorMsg,
        variant: "destructive"
      });

      return {
        success: false,
        error: errorMsg
      };
    }

    setIsDeletingDocument(true);

    try {
      logger.info(`[${operation}] Calling delete API`, {
        ...baseContext,
        sessionId,
      });

      const result = await deleteSecureDocument({
        documentId,
        sessionId,
      });

      if (!result.success) {
        const deleteError = new Error(result.error || result.details || "Document deletion failed without specific error");

        logger.error(`[${operation}] Delete failed:`, {
          ...baseContext,
          error: formatPostgresError(deleteError),
          result,
        });

        throw deleteError;
      }

      logger.info(`[${operation}] Document deleted successfully`, {
        ...baseContext,
        deletedDocumentId: result.documentId,
      });

      toast({
        title: "Document Deleted",
        description: result.message || "Document deleted successfully"
      });

      // Refresh session data to update document list
      await refreshSessionData(true);

      return {
        success: true
      };

    } catch (e: unknown) {
      const errorMessage = formatPostgresError(e);
      logger.error(`[${operation}] Operation failed:`, {
        ...baseContext,
        error: errorMessage,
      });

      toast({
        title: "Delete Error",
        description: `Failed to delete document: ${errorMessage}`,
        variant: "destructive",
      });

      return {
        success: false,
        error: errorMessage
      };

    } finally {
      setIsDeletingDocument(false);
    }
  };

  /**
   * Replaces all permission records for the given Step 4 ID with the provided list.
   */
  const setPermissions = async (permissionsToSet: TableInsert<'permissions'>[], step4Id: string): Promise<boolean> => {
    if (!step4Id) {
      logger.error("Cannot set permissions without step4Id.");
      toast({ title: "Save Error", description: "Cannot save permissions: Missing agreement context.", variant: "destructive" });
      return false;
    }
    setLoading(true);
    try {
      const { error: deleteError } = await supabase
        .schema('farms')
        .from('permissions')
        .delete()
        .eq('step_4_id', step4Id);

      if (deleteError) {
        throw deleteError;
      }

      if (permissionsToSet.length > 0) {
        const permissionsWithStepId = permissionsToSet.map(p => ({ ...p, step_4_id: step4Id }));
        const created = await createRecord('permissions', permissionsWithStepId as TableInsert<'permissions'>[]);
        if (!created || created.length !== permissionsWithStepId.length) {
          throw new Error("Failed to insert all permissions.");
        }
      }
      // refreshSessionData is called by createRecord if successful
      toast({ title: "Progress Saved", description: "Access permissions updated." });
      return true;
    } catch (e: unknown) {
      logger.error('Error setting permissions:', e);
      toast({ title: "Save Error", description: "Failed to save access permissions.", variant: "destructive" });
      return false;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Generates a time-limited signed URL for a private document in Supabase Storage.
   */
  const getSignedDocumentUrl = async (storagePath: string): Promise<string | null> => {
    if (!storagePath) {
      logger.warn('[getSignedDocumentUrl] storagePath is required.');
      return null;
    }
    try {
      const { data, error } = await supabase.storage
        .from('farms') // Assuming 'farms' is your private bucket name
        .createSignedUrl(storagePath, 3600); // 3600 seconds = 1 hour expiry

      if (error) throw error;
      return data?.signedUrl || null;
    } catch (e: unknown) {
      logger.error(`[getSignedDocumentUrl] Error creating signed URL for path "${storagePath}":`, e);
      // Do not show a global toast here as this might be called for multiple list items
      // Components using this should handle the null response and show specific UI feedback if needed
      return null;
    }
  };

  const encryptAndStoreSensitiveField = async (
    params: {
      tableName: 'bookkeeping' | 'payroll' | 'payments';
      fieldName: 'access_credentials' | 'encrypted_access_credentials' | 'bank_account_details';
      plainTextValue: string;
    }
  ): Promise<EncryptionResult> => {
    const { tableName, fieldName, plainTextValue } = params;
    const sessionId = getSessionId(sessionData);

    if (!sessionId || !isSessionDataValid(sessionData)) {
      toast({ title: "Error", description: "Session ID is missing. Cannot save credentials.", variant: "destructive" });
      return { success: false, error: "Session ID is missing" };
    }

    if ((tableName === 'bookkeeping' || tableName === 'payroll') && !getStepId(sessionData, 'step3')) {
      toast({ title: "Error", description: "Financial systems profile (Step 3) ID is missing.", variant: "destructive" });
      return { success: false, error: "Step 3 ID is missing, cannot link encrypted field." };
    }

    if (tableName === 'payments' && !getStepId(sessionData, 'step4')) {
      toast({ title: "Error", description: "Agreements profile (Step 4) ID is missing.", variant: "destructive" });
      return { success: false, error: "Step 4 ID is missing, cannot link encrypted field." };
    }

    setIsEncryptingField(true);
    try {
      const result = await invokeEncryptAndStoreSensitiveField({
        sessionId,
        tableName,
        fieldName,
        plainTextValue,
      });

      if (result.success) {
        toast({ title: "Credentials Saved", description: `${humanizeTableName(tableName)} credentials securely stored.` });
        await refreshSessionData(false); // Refresh without generic toast
      } else {
        toast({ title: "Save Failed", description: result.message || result.error || `Failed to save ${humanizeTableName(tableName)} credentials.`, variant: "destructive" });
      }
      return result;
    } catch (e: unknown) {
      const msg = e instanceof Error ? e.message : String(e);
      logger.error(`Error encrypting field for ${tableName}:`, e);
      toast({ title: "Save Error", description: msg, variant: "destructive" });
      return { success: false, error: msg };
    } finally {
      setIsEncryptingField(false);
    }
  };

  const submitOnboarding = async (): Promise<SubmitOnboardingResult> => {
    const sessionId = getSessionId(sessionData);
    if (!sessionId || !isSessionDataValid(sessionData)) {
      toast({ title: "Error", description: "Session ID is missing. Cannot submit onboarding.", variant: "destructive" });
      return { success: false, error: "Session ID is missing" };
    }
    setIsSubmittingSession(true);
    try {
      const result = await submitOnboardingSession(sessionId);
      if (result.success) {
        toast({ title: "Onboarding Submitted!", description: result.message || "Your onboarding information has been successfully submitted." });
        // Optimistically update status, then refresh for authoritative data
        setSessionData(prev =>
          prev ? {
            ...prev,
            onboardingSession: {
              ...prev.onboardingSession!,
              status: 'completed' // Changed from 'submitted' to 'completed'
            }
          } : null
        );
        await refreshSessionData(false);
      } else {
        // Prepare a more detailed error message if validation errors are provided
        let detailedErrorDescription = result.error || result.message || "An unknown error occurred during submission.";
        if (result.errors && Array.isArray(result.errors) && result.errors.length > 0) {
          detailedErrorDescription += "\n\nDetails:\n- " + result.errors.join("\n- ");
        }
        toast({
          title: "Submission Failed",
          description: detailedErrorDescription,
          variant: "destructive",
          duration: 15000 // Longer duration for detailed errors
        });
      }
      return result;
    } catch (e: unknown) {
      const msg = e instanceof Error ? e.message : String(e);
      logger.error('Error submitting onboarding session:', e);
      toast({ title: "Submission Error", description: msg, variant: "destructive" });
      return { success: false, error: msg };
    } finally {
      setIsSubmittingSession(false);
    }
  };

  const value: OnboardingContextType = {
    sessionData,
    loading,
    error,
    currentStep,
    updateCurrentStep,
    refreshSessionData,
    sessionId: getSessionId(sessionData),
    isEncryptingField,
    isSubmittingSession,
    isProcessingSignature,

    createRecord,
    updateRecord,
    deleteRecord,
    upsertRecord,
    ensureLinkedRecord,

    ensureStep1BusinessProfileRecordExists,
    ensureStep2FarmOperationsRecordExists,
    ensureStep3FinancialSystemsRecordExists,
    ensureStep4RegistrationsInsuranceRecordExists,
    ensureStep5FinalizationRecordExists,

    // --- DEPRECATED: Legacy Step 4 Agreements (Backward Compatibility) ---
    ensureStep4AgreementsRecordExists,

    ensureDataMigrationRecordExists,
    ensureCommunicationPreferencesRecordExists,
    ensurePaymentRecordExists,

    invokeImportAssetsCSV,
    invokeProcessDigitalSignature,
    uploadAndFinalizeDocument,
    deleteDocument,

    encryptAndStoreSensitiveField,
    submitOnboarding,

    setPermissions,
    getSignedDocumentUrl,

    // --- Helper Functions for External Components ---
    helpers: {
      getStepData,
      getStepId,
      isSessionDataValid,
      getSessionId,
      getCurrentStep,
      getSessionStatus,
      formatPhoneNumber,
      formatAbn,
      formatAcn,
      validateABN,
      validateACN,
      validateEmail,
      validateAustralianPhone,
      humanizeTableName,
      formatFieldName,
    },
  };

  return <OnboardingContext.Provider value={value}>{children}</OnboardingContext.Provider>;
};

/**
 * Convenience hook to access the `OnboardingContext`.
 * Throws an error if used outside of `OnboardingProvider`.
 */
export const useOnboarding = (): OnboardingContextType => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};
