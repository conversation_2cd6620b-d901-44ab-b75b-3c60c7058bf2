export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  farms: {
    Tables: {
      activities: {
        Row: {
          activity_description: string | null
          activity_type: Database["farms"]["Enums"]["activity_type_enum"]
          approximate_numbers: number | null
          certification_status: string | null
          created_at: string
          crop_type: string | null
          crop_varieties: string[] | null
          id: string
          livestock_type: string | null
          primary_market: string | null
          production_method: string | null
          seasonal_pattern: string | null
          step_2_id: string
          updated_at: string
        }
        Insert: {
          activity_description?: string | null
          activity_type: Database["farms"]["Enums"]["activity_type_enum"]
          approximate_numbers?: number | null
          certification_status?: string | null
          created_at?: string
          crop_type?: string | null
          crop_varieties?: string[] | null
          id?: string
          livestock_type?: string | null
          primary_market?: string | null
          production_method?: string | null
          seasonal_pattern?: string | null
          step_2_id: string
          updated_at?: string
        }
        Update: {
          activity_description?: string | null
          activity_type?: Database["farms"]["Enums"]["activity_type_enum"]
          approximate_numbers?: number | null
          certification_status?: string | null
          created_at?: string
          crop_type?: string | null
          crop_varieties?: string[] | null
          id?: string
          livestock_type?: string | null
          primary_market?: string | null
          production_method?: string | null
          seasonal_pattern?: string | null
          step_2_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "activities_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: false
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
        ]
      }
      addresses: {
        Row: {
          address_type: string
          country: string | null
          created_at: string
          full_address_text: string
          id: string
          locality: string | null
          postcode: string | null
          state: string | null
          step_1_id: string
          street: string | null
          updated_at: string
        }
        Insert: {
          address_type: string
          country?: string | null
          created_at?: string
          full_address_text: string
          id?: string
          locality?: string | null
          postcode?: string | null
          state?: string | null
          step_1_id: string
          street?: string | null
          updated_at?: string
        }
        Update: {
          address_type?: string
          country?: string | null
          created_at?: string
          full_address_text?: string
          id?: string
          locality?: string | null
          postcode?: string | null
          state?: string | null
          step_1_id?: string
          street?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "addresses_step_1_id_fkey"
            columns: ["step_1_id"]
            isOneToOne: false
            referencedRelation: "step_1_business_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      agreements: {
        Row: {
          agreed_at: string | null
          agreement_document_path: string | null
          agreement_type: Database["farms"]["Enums"]["agreement_type_enum"]
          agreement_version: string | null
          consent_method: string | null
          created_at: string
          id: string
          ip_address: unknown | null
          is_agreed: boolean
          signatory_authority_level: string | null
          signatory_name: string | null
          signatory_title: string | null
          signature_data: string | null
          signature_method: string | null
          signature_storage_path: string | null
          step_5_id: string
          updated_at: string
          user_agent: string | null
          witness_name: string | null
          witness_signature_path: string | null
        }
        Insert: {
          agreed_at?: string | null
          agreement_document_path?: string | null
          agreement_type: Database["farms"]["Enums"]["agreement_type_enum"]
          agreement_version?: string | null
          consent_method?: string | null
          created_at?: string
          id?: string
          ip_address?: unknown | null
          is_agreed?: boolean
          signatory_authority_level?: string | null
          signatory_name?: string | null
          signatory_title?: string | null
          signature_data?: string | null
          signature_method?: string | null
          signature_storage_path?: string | null
          step_5_id: string
          updated_at?: string
          user_agent?: string | null
          witness_name?: string | null
          witness_signature_path?: string | null
        }
        Update: {
          agreed_at?: string | null
          agreement_document_path?: string | null
          agreement_type?: Database["farms"]["Enums"]["agreement_type_enum"]
          agreement_version?: string | null
          consent_method?: string | null
          created_at?: string
          id?: string
          ip_address?: unknown | null
          is_agreed?: boolean
          signatory_authority_level?: string | null
          signatory_name?: string | null
          signatory_title?: string | null
          signature_data?: string | null
          signature_method?: string | null
          signature_storage_path?: string | null
          step_5_id?: string
          updated_at?: string
          user_agent?: string | null
          witness_name?: string | null
          witness_signature_path?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agreements_step_5_id_fkey"
            columns: ["step_5_id"]
            isOneToOne: false
            referencedRelation: "step_5_finalization"
            referencedColumns: ["id"]
          },
        ]
      }
      assets: {
        Row: {
          asset_category: Database["farms"]["Enums"]["asset_category_enum"]
          asset_type: string
          coverage_amount: number | null
          created_at: string
          excess_amount: number | null
          id: string
          make_or_provider: string | null
          model_year: number | null
          policy_type: string | null
          purchase_date: string | null
          purchase_price: number | null
          registration_or_policy_number: string | null
          renewal_date: string | null
          serial_number: string | null
          step_3_id: string
          updated_at: string
        }
        Insert: {
          asset_category: Database["farms"]["Enums"]["asset_category_enum"]
          asset_type: string
          coverage_amount?: number | null
          created_at?: string
          excess_amount?: number | null
          id?: string
          make_or_provider?: string | null
          model_year?: number | null
          policy_type?: string | null
          purchase_date?: string | null
          purchase_price?: number | null
          registration_or_policy_number?: string | null
          renewal_date?: string | null
          serial_number?: string | null
          step_3_id: string
          updated_at?: string
        }
        Update: {
          asset_category?: Database["farms"]["Enums"]["asset_category_enum"]
          asset_type?: string
          coverage_amount?: number | null
          created_at?: string
          excess_amount?: number | null
          id?: string
          make_or_provider?: string | null
          model_year?: number | null
          policy_type?: string | null
          purchase_date?: string | null
          purchase_price?: number | null
          registration_or_policy_number?: string | null
          renewal_date?: string | null
          serial_number?: string | null
          step_3_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "assets_step_3_id_fkey"
            columns: ["step_3_id"]
            isOneToOne: false
            referencedRelation: "step_3_financial_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      banking_info: {
        Row: {
          account_purpose: string
          account_type: string
          bank_feed_provider: string | null
          bank_name: string
          branch_location: string | null
          created_at: string
          encrypted_account_details: string | null
          feed_frequency: string | null
          has_bank_feeds: boolean
          id: string
          relationship_manager: string | null
          step_3_id: string
          updated_at: string
        }
        Insert: {
          account_purpose: string
          account_type: string
          bank_feed_provider?: string | null
          bank_name: string
          branch_location?: string | null
          created_at?: string
          encrypted_account_details?: string | null
          feed_frequency?: string | null
          has_bank_feeds?: boolean
          id?: string
          relationship_manager?: string | null
          step_3_id: string
          updated_at?: string
        }
        Update: {
          account_purpose?: string
          account_type?: string
          bank_feed_provider?: string | null
          bank_name?: string
          branch_location?: string | null
          created_at?: string
          encrypted_account_details?: string | null
          feed_frequency?: string | null
          has_bank_feeds?: boolean
          id?: string
          relationship_manager?: string | null
          step_3_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "banking_info_step_3_id_fkey"
            columns: ["step_3_id"]
            isOneToOne: false
            referencedRelation: "step_3_financial_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      bookkeeping: {
        Row: {
          access_credentials: string | null
          accountant_access_level: string | null
          accountant_has_access: boolean
          accounting_method: Database["farms"]["Enums"]["accounting_method_enum"]
          bas_lodgement_frequency: Database["farms"]["Enums"]["bas_frequency_enum"]
          bas_preparation: Database["farms"]["Enums"]["bas_preparation_enum"]
          chart_of_accounts_setup: Database["farms"]["Enums"]["chart_of_accounts_setup_enum"]
          created_at: string
          current_software: string
          financial_year_end_month: number
          has_bank_feeds_enabled: boolean
          id: string
          software_version: string | null
          step_3_id: string
          updated_at: string
        }
        Insert: {
          access_credentials?: string | null
          accountant_access_level?: string | null
          accountant_has_access?: boolean
          accounting_method: Database["farms"]["Enums"]["accounting_method_enum"]
          bas_lodgement_frequency: Database["farms"]["Enums"]["bas_frequency_enum"]
          bas_preparation: Database["farms"]["Enums"]["bas_preparation_enum"]
          chart_of_accounts_setup: Database["farms"]["Enums"]["chart_of_accounts_setup_enum"]
          created_at?: string
          current_software: string
          financial_year_end_month: number
          has_bank_feeds_enabled?: boolean
          id?: string
          software_version?: string | null
          step_3_id: string
          updated_at?: string
        }
        Update: {
          access_credentials?: string | null
          accountant_access_level?: string | null
          accountant_has_access?: boolean
          accounting_method?: Database["farms"]["Enums"]["accounting_method_enum"]
          bas_lodgement_frequency?: Database["farms"]["Enums"]["bas_frequency_enum"]
          bas_preparation?: Database["farms"]["Enums"]["bas_preparation_enum"]
          chart_of_accounts_setup?: Database["farms"]["Enums"]["chart_of_accounts_setup_enum"]
          created_at?: string
          current_software?: string
          financial_year_end_month?: number
          has_bank_feeds_enabled?: boolean
          id?: string
          software_version?: string | null
          step_3_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "bookkeeping_step_3_id_fkey"
            columns: ["step_3_id"]
            isOneToOne: true
            referencedRelation: "step_3_financial_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      business_registration: {
        Row: {
          abn: string
          acn: string | null
          business_structure: Database["farms"]["Enums"]["business_structure_enum"]
          created_at: string
          full_business_name: string
          id: string
          is_gst_registered: boolean
          primary_business_phone: string | null
          step_1_id: string
          trading_name: string | null
          updated_at: string
        }
        Insert: {
          abn: string
          acn?: string | null
          business_structure: Database["farms"]["Enums"]["business_structure_enum"]
          created_at?: string
          full_business_name: string
          id?: string
          is_gst_registered?: boolean
          primary_business_phone?: string | null
          step_1_id: string
          trading_name?: string | null
          updated_at?: string
        }
        Update: {
          abn?: string
          acn?: string | null
          business_structure?: Database["farms"]["Enums"]["business_structure_enum"]
          created_at?: string
          full_business_name?: string
          id?: string
          is_gst_registered?: boolean
          primary_business_phone?: string | null
          step_1_id?: string
          trading_name?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "business_registration_step_1_id_fkey"
            columns: ["step_1_id"]
            isOneToOne: true
            referencedRelation: "step_1_business_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      chemical_usage: {
        Row: {
          active_ingredient: string | null
          application_method: string | null
          application_rate: string | null
          created_at: string
          id: string
          last_application_date: string | null
          manufacturer: string | null
          product_name: string
          step_2_id: string
          target_block_area: number | null
          updated_at: string
          usage_purpose: string
          weather_conditions: string | null
          withholding_period_days: number | null
        }
        Insert: {
          active_ingredient?: string | null
          application_method?: string | null
          application_rate?: string | null
          created_at?: string
          id?: string
          last_application_date?: string | null
          manufacturer?: string | null
          product_name: string
          step_2_id: string
          target_block_area?: number | null
          updated_at?: string
          usage_purpose: string
          weather_conditions?: string | null
          withholding_period_days?: number | null
        }
        Update: {
          active_ingredient?: string | null
          application_method?: string | null
          application_rate?: string | null
          created_at?: string
          id?: string
          last_application_date?: string | null
          manufacturer?: string | null
          product_name?: string
          step_2_id?: string
          target_block_area?: number | null
          updated_at?: string
          usage_purpose?: string
          weather_conditions?: string | null
          withholding_period_days?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "chemical_usage_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: false
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
        ]
      }
      communication_preferences: {
        Row: {
          accessibility_requirements: string | null
          avoid_contact_periods: string[] | null
          business_hours_only: boolean
          compliance_alerts_email: string | null
          created_at: string
          cultural_considerations: string | null
          emergency_contact_method:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          financial_reports_email: string | null
          id: string
          language_preference: string | null
          preferred_contact_day_of_week: number | null
          preferred_contact_times: string
          preferred_methods: Database["farms"]["Enums"]["communication_method_enum"][]
          preferred_report_day_of_month: number | null
          primary_email: string
          primary_phone: string | null
          reporting_frequency: Database["farms"]["Enums"]["reporting_frequency_enum"]
          secondary_email: string | null
          secondary_phone: string | null
          step_5_id: string
          system_notifications_email: string | null
          timezone: string | null
          updated_at: string
          whatsapp_number: string | null
        }
        Insert: {
          accessibility_requirements?: string | null
          avoid_contact_periods?: string[] | null
          business_hours_only?: boolean
          compliance_alerts_email?: string | null
          created_at?: string
          cultural_considerations?: string | null
          emergency_contact_method?:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          financial_reports_email?: string | null
          id?: string
          language_preference?: string | null
          preferred_contact_day_of_week?: number | null
          preferred_contact_times: string
          preferred_methods: Database["farms"]["Enums"]["communication_method_enum"][]
          preferred_report_day_of_month?: number | null
          primary_email: string
          primary_phone?: string | null
          reporting_frequency: Database["farms"]["Enums"]["reporting_frequency_enum"]
          secondary_email?: string | null
          secondary_phone?: string | null
          step_5_id: string
          system_notifications_email?: string | null
          timezone?: string | null
          updated_at?: string
          whatsapp_number?: string | null
        }
        Update: {
          accessibility_requirements?: string | null
          avoid_contact_periods?: string[] | null
          business_hours_only?: boolean
          compliance_alerts_email?: string | null
          created_at?: string
          cultural_considerations?: string | null
          emergency_contact_method?:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          financial_reports_email?: string | null
          id?: string
          language_preference?: string | null
          preferred_contact_day_of_week?: number | null
          preferred_contact_times?: string
          preferred_methods?: Database["farms"]["Enums"]["communication_method_enum"][]
          preferred_report_day_of_month?: number | null
          primary_email?: string
          primary_phone?: string | null
          reporting_frequency?: Database["farms"]["Enums"]["reporting_frequency_enum"]
          secondary_email?: string | null
          secondary_phone?: string | null
          step_5_id?: string
          system_notifications_email?: string | null
          timezone?: string | null
          updated_at?: string
          whatsapp_number?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "communication_preferences_step_5_id_fkey"
            columns: ["step_5_id"]
            isOneToOne: true
            referencedRelation: "step_5_finalization"
            referencedColumns: ["id"]
          },
        ]
      }
      contacts: {
        Row: {
          authority_level:
            | Database["farms"]["Enums"]["authority_level_enum"]
            | null
          contact_type: Database["farms"]["Enums"]["contact_type_enum"]
          created_at: string
          email: string | null
          id: string
          name: string
          phone: string | null
          step_1_id: string
          title_or_firm: string | null
          title_position: string | null
          updated_at: string
        }
        Insert: {
          authority_level?:
            | Database["farms"]["Enums"]["authority_level_enum"]
            | null
          contact_type: Database["farms"]["Enums"]["contact_type_enum"]
          created_at?: string
          email?: string | null
          id?: string
          name: string
          phone?: string | null
          step_1_id: string
          title_or_firm?: string | null
          title_position?: string | null
          updated_at?: string
        }
        Update: {
          authority_level?:
            | Database["farms"]["Enums"]["authority_level_enum"]
            | null
          contact_type?: Database["farms"]["Enums"]["contact_type_enum"]
          created_at?: string
          email?: string | null
          id?: string
          name?: string
          phone?: string | null
          step_1_id?: string
          title_or_firm?: string | null
          title_position?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contacts_step_1_id_fkey"
            columns: ["step_1_id"]
            isOneToOne: false
            referencedRelation: "step_1_business_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      contracts: {
        Row: {
          contract_description: string
          contract_type: string | null
          contract_value: number | null
          contracting_party: string | null
          created_at: string
          expiry_date: string | null
          id: string
          payment_terms: string | null
          start_date: string | null
          step_2_id: string
          supplier_id: string | null
          updated_at: string
        }
        Insert: {
          contract_description: string
          contract_type?: string | null
          contract_value?: number | null
          contracting_party?: string | null
          created_at?: string
          expiry_date?: string | null
          id?: string
          payment_terms?: string | null
          start_date?: string | null
          step_2_id: string
          supplier_id?: string | null
          updated_at?: string
        }
        Update: {
          contract_description?: string
          contract_type?: string | null
          contract_value?: number | null
          contracting_party?: string | null
          created_at?: string
          expiry_date?: string | null
          id?: string
          payment_terms?: string | null
          start_date?: string | null
          step_2_id?: string
          supplier_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contracts_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: false
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contracts_supplier_id_fkey"
            columns: ["supplier_id"]
            isOneToOne: false
            referencedRelation: "suppliers"
            referencedColumns: ["id"]
          },
        ]
      }
      data_migration: {
        Row: {
          created_at: string
          custom_field_mapping_needed: boolean
          data_cleanup_required: boolean
          document_categories: string[] | null
          estimated_document_count: number | null
          filing_system_description: string
          folder_structure_description: string | null
          id: string
          integration_complexity_level: string | null
          legacy_system_access_needed: boolean
          migration_priority_order: string[] | null
          preferred_migration_timeline: string
          primary_cloud_storage: string
          requires_data_mapping: boolean
          secondary_cloud_storage: string | null
          step_5_id: string
          total_estimated_storage_gb: number | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          custom_field_mapping_needed?: boolean
          data_cleanup_required?: boolean
          document_categories?: string[] | null
          estimated_document_count?: number | null
          filing_system_description: string
          folder_structure_description?: string | null
          id?: string
          integration_complexity_level?: string | null
          legacy_system_access_needed?: boolean
          migration_priority_order?: string[] | null
          preferred_migration_timeline: string
          primary_cloud_storage: string
          requires_data_mapping?: boolean
          secondary_cloud_storage?: string | null
          step_5_id: string
          total_estimated_storage_gb?: number | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          custom_field_mapping_needed?: boolean
          data_cleanup_required?: boolean
          document_categories?: string[] | null
          estimated_document_count?: number | null
          filing_system_description?: string
          folder_structure_description?: string | null
          id?: string
          integration_complexity_level?: string | null
          legacy_system_access_needed?: boolean
          migration_priority_order?: string[] | null
          preferred_migration_timeline?: string
          primary_cloud_storage?: string
          requires_data_mapping?: boolean
          secondary_cloud_storage?: string | null
          step_5_id?: string
          total_estimated_storage_gb?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "data_migration_step_5_id_fkey"
            columns: ["step_5_id"]
            isOneToOne: true
            referencedRelation: "step_5_finalization"
            referencedColumns: ["id"]
          },
        ]
      }
      external_accountant: {
        Row: {
          address: string | null
          contact_person: string
          created_at: string
          email: string | null
          engagement_type: string | null
          fee_structure: string | null
          firm_name: string
          has_software_access: boolean
          id: string
          phone: string | null
          preferred_communication_method:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          services_provided: string[] | null
          software_access_level: string | null
          step_3_id: string
          updated_at: string
        }
        Insert: {
          address?: string | null
          contact_person: string
          created_at?: string
          email?: string | null
          engagement_type?: string | null
          fee_structure?: string | null
          firm_name: string
          has_software_access?: boolean
          id?: string
          phone?: string | null
          preferred_communication_method?:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          services_provided?: string[] | null
          software_access_level?: string | null
          step_3_id: string
          updated_at?: string
        }
        Update: {
          address?: string | null
          contact_person?: string
          created_at?: string
          email?: string | null
          engagement_type?: string | null
          fee_structure?: string | null
          firm_name?: string
          has_software_access?: boolean
          id?: string
          phone?: string | null
          preferred_communication_method?:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          services_provided?: string[] | null
          software_access_level?: string | null
          step_3_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "external_accountant_step_3_id_fkey"
            columns: ["step_3_id"]
            isOneToOne: true
            referencedRelation: "step_3_financial_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      farm_blocks: {
        Row: {
          block_name: string
          block_size_acres: number | null
          block_size_hectares: number | null
          boundary_description: string | null
          created_at: string
          drainage_quality: string | null
          gps_coordinates: string | null
          id: string
          irrigation_type: string | null
          primary_use: string
          slope_percentage: number | null
          soil_type: string | null
          step_2_id: string
          updated_at: string
        }
        Insert: {
          block_name: string
          block_size_acres?: number | null
          block_size_hectares?: number | null
          boundary_description?: string | null
          created_at?: string
          drainage_quality?: string | null
          gps_coordinates?: string | null
          id?: string
          irrigation_type?: string | null
          primary_use: string
          slope_percentage?: number | null
          soil_type?: string | null
          step_2_id: string
          updated_at?: string
        }
        Update: {
          block_name?: string
          block_size_acres?: number | null
          block_size_hectares?: number | null
          boundary_description?: string | null
          created_at?: string
          drainage_quality?: string | null
          gps_coordinates?: string | null
          id?: string
          irrigation_type?: string | null
          primary_use?: string
          slope_percentage?: number | null
          soil_type?: string | null
          step_2_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "farm_blocks_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: false
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
        ]
      }
      farm_info: {
        Row: {
          average_annual_rainfall_mm: number | null
          climate_zone: string | null
          created_at: string
          farm_established_year: number | null
          id: string
          primary_land_use: string | null
          soil_types: string[] | null
          step_2_id: string
          total_farm_size_acres: number | null
          total_farm_size_hectares: number | null
          updated_at: string
          water_sources: string[] | null
        }
        Insert: {
          average_annual_rainfall_mm?: number | null
          climate_zone?: string | null
          created_at?: string
          farm_established_year?: number | null
          id?: string
          primary_land_use?: string | null
          soil_types?: string[] | null
          step_2_id: string
          total_farm_size_acres?: number | null
          total_farm_size_hectares?: number | null
          updated_at?: string
          water_sources?: string[] | null
        }
        Update: {
          average_annual_rainfall_mm?: number | null
          climate_zone?: string | null
          created_at?: string
          farm_established_year?: number | null
          id?: string
          primary_land_use?: string | null
          soil_types?: string[] | null
          step_2_id?: string
          total_farm_size_acres?: number | null
          total_farm_size_hectares?: number | null
          updated_at?: string
          water_sources?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "farm_info_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: true
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
        ]
      }
      finalization_submission: {
        Row: {
          assigned_to_staff_member: string | null
          created_at: string
          data_completeness_score: number | null
          data_processing_consent_given: boolean
          estimated_setup_completion_date: string | null
          id: string
          internal_processing_status: string | null
          manual_review_reason: string | null
          manual_review_required: boolean
          privacy_policy_accepted: boolean
          processing_notes: string | null
          service_agreement_accepted: boolean
          step_5_id: string
          submission_ip_address: unknown | null
          submission_location: string | null
          submission_status: string
          submission_user_agent: string | null
          submitted_at: string | null
          submitted_by_contact_id: string | null
          terms_and_conditions_accepted: boolean
          updated_at: string
          validation_errors: string[] | null
          validation_warnings: string[] | null
        }
        Insert: {
          assigned_to_staff_member?: string | null
          created_at?: string
          data_completeness_score?: number | null
          data_processing_consent_given?: boolean
          estimated_setup_completion_date?: string | null
          id?: string
          internal_processing_status?: string | null
          manual_review_reason?: string | null
          manual_review_required?: boolean
          privacy_policy_accepted?: boolean
          processing_notes?: string | null
          service_agreement_accepted?: boolean
          step_5_id: string
          submission_ip_address?: unknown | null
          submission_location?: string | null
          submission_status?: string
          submission_user_agent?: string | null
          submitted_at?: string | null
          submitted_by_contact_id?: string | null
          terms_and_conditions_accepted?: boolean
          updated_at?: string
          validation_errors?: string[] | null
          validation_warnings?: string[] | null
        }
        Update: {
          assigned_to_staff_member?: string | null
          created_at?: string
          data_completeness_score?: number | null
          data_processing_consent_given?: boolean
          estimated_setup_completion_date?: string | null
          id?: string
          internal_processing_status?: string | null
          manual_review_reason?: string | null
          manual_review_required?: boolean
          privacy_policy_accepted?: boolean
          processing_notes?: string | null
          service_agreement_accepted?: boolean
          step_5_id?: string
          submission_ip_address?: unknown | null
          submission_location?: string | null
          submission_status?: string
          submission_user_agent?: string | null
          submitted_at?: string | null
          submitted_by_contact_id?: string | null
          terms_and_conditions_accepted?: boolean
          updated_at?: string
          validation_errors?: string[] | null
          validation_warnings?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "finalization_submission_step_5_id_fkey"
            columns: ["step_5_id"]
            isOneToOne: true
            referencedRelation: "step_5_finalization"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "finalization_submission_submitted_by_contact_id_fkey"
            columns: ["submitted_by_contact_id"]
            isOneToOne: false
            referencedRelation: "contacts"
            referencedColumns: ["id"]
          },
        ]
      }
      financial_reporting_config: {
        Row: {
          audit_frequency: string | null
          benchmark_targets: Json | null
          board_reporting_required: boolean
          created_at: string
          financial_year_end: string
          id: string
          key_performance_indicators: string[] | null
          regulatory_requirements: string[] | null
          reporting_frequency: Database["farms"]["Enums"]["reporting_frequency_enum"]
          required_reports: string[]
          stakeholder_reports: string[] | null
          step_3_id: string
          updated_at: string
        }
        Insert: {
          audit_frequency?: string | null
          benchmark_targets?: Json | null
          board_reporting_required?: boolean
          created_at?: string
          financial_year_end: string
          id?: string
          key_performance_indicators?: string[] | null
          regulatory_requirements?: string[] | null
          reporting_frequency: Database["farms"]["Enums"]["reporting_frequency_enum"]
          required_reports: string[]
          stakeholder_reports?: string[] | null
          step_3_id: string
          updated_at?: string
        }
        Update: {
          audit_frequency?: string | null
          benchmark_targets?: Json | null
          board_reporting_required?: boolean
          created_at?: string
          financial_year_end?: string
          id?: string
          key_performance_indicators?: string[] | null
          regulatory_requirements?: string[] | null
          reporting_frequency?: Database["farms"]["Enums"]["reporting_frequency_enum"]
          required_reports?: string[]
          stakeholder_reports?: string[] | null
          step_3_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "financial_reporting_config_step_3_id_fkey"
            columns: ["step_3_id"]
            isOneToOne: true
            referencedRelation: "step_3_financial_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      insurance_policies: {
        Row: {
          annual_premium: number | null
          broker_contact: string | null
          claims_contact: string | null
          coverage_amount: number | null
          coverage_description: string | null
          created_at: string
          excess_amount: number | null
          id: string
          insurance_type: Database["farms"]["Enums"]["insurance_type_enum"]
          insurer_name: string
          payment_frequency: string | null
          policy_conditions: string | null
          policy_expiry_date: string
          policy_number: string
          policy_start_date: string
          risk_category: string | null
          special_conditions: string[] | null
          step_4_id: string
          updated_at: string
        }
        Insert: {
          annual_premium?: number | null
          broker_contact?: string | null
          claims_contact?: string | null
          coverage_amount?: number | null
          coverage_description?: string | null
          created_at?: string
          excess_amount?: number | null
          id?: string
          insurance_type: Database["farms"]["Enums"]["insurance_type_enum"]
          insurer_name: string
          payment_frequency?: string | null
          policy_conditions?: string | null
          policy_expiry_date: string
          policy_number: string
          policy_start_date: string
          risk_category?: string | null
          special_conditions?: string[] | null
          step_4_id: string
          updated_at?: string
        }
        Update: {
          annual_premium?: number | null
          broker_contact?: string | null
          claims_contact?: string | null
          coverage_amount?: number | null
          coverage_description?: string | null
          created_at?: string
          excess_amount?: number | null
          id?: string
          insurance_type?: Database["farms"]["Enums"]["insurance_type_enum"]
          insurer_name?: string
          payment_frequency?: string | null
          policy_conditions?: string | null
          policy_expiry_date?: string
          policy_number?: string
          policy_start_date?: string
          risk_category?: string | null
          special_conditions?: string[] | null
          step_4_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "insurance_policies_step_4_id_fkey"
            columns: ["step_4_id"]
            isOneToOne: false
            referencedRelation: "step_4_registrations_insurance"
            referencedColumns: ["id"]
          },
        ]
      }
      integration_settings: {
        Row: {
          api_access_required: boolean
          auto_sync_enabled: boolean
          backup_frequency: string
          created_at: string
          data_retention_period_months: number
          error_notification_method:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          existing_software_systems: string[] | null
          id: string
          preferred_integrations: string[] | null
          step_3_id: string
          sync_frequency: string
          sync_notifications_enabled: boolean
          updated_at: string
          webhook_endpoints: string[] | null
        }
        Insert: {
          api_access_required?: boolean
          auto_sync_enabled?: boolean
          backup_frequency: string
          created_at?: string
          data_retention_period_months?: number
          error_notification_method?:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          existing_software_systems?: string[] | null
          id?: string
          preferred_integrations?: string[] | null
          step_3_id: string
          sync_frequency: string
          sync_notifications_enabled?: boolean
          updated_at?: string
          webhook_endpoints?: string[] | null
        }
        Update: {
          api_access_required?: boolean
          auto_sync_enabled?: boolean
          backup_frequency?: string
          created_at?: string
          data_retention_period_months?: number
          error_notification_method?:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          existing_software_systems?: string[] | null
          id?: string
          preferred_integrations?: string[] | null
          step_3_id?: string
          sync_frequency?: string
          sync_notifications_enabled?: boolean
          updated_at?: string
          webhook_endpoints?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "integration_settings_step_3_id_fkey"
            columns: ["step_3_id"]
            isOneToOne: true
            referencedRelation: "step_3_financial_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      key_staff: {
        Row: {
          contact_email: string | null
          contact_phone: string | null
          created_at: string
          department_area: string | null
          employment_type:
            | Database["farms"]["Enums"]["employment_type_enum"]
            | null
          id: string
          role_or_title: string | null
          staff_name: string
          step_1_id: string
          supervisor_id: string | null
          updated_at: string
        }
        Insert: {
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string
          department_area?: string | null
          employment_type?:
            | Database["farms"]["Enums"]["employment_type_enum"]
            | null
          id?: string
          role_or_title?: string | null
          staff_name: string
          step_1_id: string
          supervisor_id?: string | null
          updated_at?: string
        }
        Update: {
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string
          department_area?: string | null
          employment_type?:
            | Database["farms"]["Enums"]["employment_type_enum"]
            | null
          id?: string
          role_or_title?: string | null
          staff_name?: string
          step_1_id?: string
          supervisor_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "key_staff_step_1_id_fkey"
            columns: ["step_1_id"]
            isOneToOne: false
            referencedRelation: "step_1_business_profile"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "key_staff_supervisor_id_fkey"
            columns: ["supervisor_id"]
            isOneToOne: false
            referencedRelation: "contacts"
            referencedColumns: ["id"]
          },
        ]
      }
      licenses_new: {
        Row: {
          compliance_notes: string | null
          created_at: string
          expiry_date: string | null
          id: string
          issue_date: string | null
          issuing_authority: string | null
          last_renewal_date: string | null
          license_number: string | null
          license_status: Database["farms"]["Enums"]["license_status_enum"]
          license_type: Database["farms"]["Enums"]["license_type_enum"]
          next_renewal_reminder_date: string | null
          renewal_cost: number | null
          renewal_frequency:
            | Database["farms"]["Enums"]["renewal_frequency_enum"]
            | null
          step_4_id: string
          updated_at: string
        }
        Insert: {
          compliance_notes?: string | null
          created_at?: string
          expiry_date?: string | null
          id?: string
          issue_date?: string | null
          issuing_authority?: string | null
          last_renewal_date?: string | null
          license_number?: string | null
          license_status?: Database["farms"]["Enums"]["license_status_enum"]
          license_type: Database["farms"]["Enums"]["license_type_enum"]
          next_renewal_reminder_date?: string | null
          renewal_cost?: number | null
          renewal_frequency?:
            | Database["farms"]["Enums"]["renewal_frequency_enum"]
            | null
          step_4_id: string
          updated_at?: string
        }
        Update: {
          compliance_notes?: string | null
          created_at?: string
          expiry_date?: string | null
          id?: string
          issue_date?: string | null
          issuing_authority?: string | null
          last_renewal_date?: string | null
          license_number?: string | null
          license_status?: Database["farms"]["Enums"]["license_status_enum"]
          license_type?: Database["farms"]["Enums"]["license_type_enum"]
          next_renewal_reminder_date?: string | null
          renewal_cost?: number | null
          renewal_frequency?:
            | Database["farms"]["Enums"]["renewal_frequency_enum"]
            | null
          step_4_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "licenses_new_step_4_id_fkey"
            columns: ["step_4_id"]
            isOneToOne: false
            referencedRelation: "step_4_registrations_insurance"
            referencedColumns: ["id"]
          },
        ]
      }
      onboarding_sessions: {
        Row: {
          created_at: string
          current_step: number
          id: string
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          current_step?: number
          id?: string
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          current_step?: number
          id?: string
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      payments: {
        Row: {
          authorized_by_contact_id: string | null
          backup_payment_details: string | null
          backup_payment_method: string | null
          bank_account_details: string
          created_at: string
          direct_debit_authority_date: string | null
          direct_debit_authority_signed: boolean
          id: string
          late_payment_fee_accepted: boolean
          payment_frequency: string | null
          payment_method: string | null
          payment_notification_email: string | null
          payment_notification_method:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          payment_terms_accepted: boolean
          payment_terms_version: string | null
          preferred_payment_date: number | null
          step_5_id: string
          updated_at: string
        }
        Insert: {
          authorized_by_contact_id?: string | null
          backup_payment_details?: string | null
          backup_payment_method?: string | null
          bank_account_details: string
          created_at?: string
          direct_debit_authority_date?: string | null
          direct_debit_authority_signed?: boolean
          id?: string
          late_payment_fee_accepted?: boolean
          payment_frequency?: string | null
          payment_method?: string | null
          payment_notification_email?: string | null
          payment_notification_method?:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          payment_terms_accepted?: boolean
          payment_terms_version?: string | null
          preferred_payment_date?: number | null
          step_5_id: string
          updated_at?: string
        }
        Update: {
          authorized_by_contact_id?: string | null
          backup_payment_details?: string | null
          backup_payment_method?: string | null
          bank_account_details?: string
          created_at?: string
          direct_debit_authority_date?: string | null
          direct_debit_authority_signed?: boolean
          id?: string
          late_payment_fee_accepted?: boolean
          payment_frequency?: string | null
          payment_method?: string | null
          payment_notification_email?: string | null
          payment_notification_method?:
            | Database["farms"]["Enums"]["communication_method_enum"]
            | null
          payment_terms_accepted?: boolean
          payment_terms_version?: string | null
          preferred_payment_date?: number | null
          step_5_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payments_authorized_by_contact_id_fkey"
            columns: ["authorized_by_contact_id"]
            isOneToOne: false
            referencedRelation: "contacts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_step_5_id_fkey"
            columns: ["step_5_id"]
            isOneToOne: true
            referencedRelation: "step_5_finalization"
            referencedColumns: ["id"]
          },
        ]
      }
      payroll: {
        Row: {
          created_at: string
          current_payroll_software: string | null
          employee_count: number | null
          encrypted_access_credentials: string | null
          id: string
          is_access_to_software_granted: boolean
          is_payroll_processing_needed: boolean
          payroll_frequency: string | null
          step_3_id: string
          superannuation_fund: string | null
          updated_at: string
          workers_compensation_policy: string | null
        }
        Insert: {
          created_at?: string
          current_payroll_software?: string | null
          employee_count?: number | null
          encrypted_access_credentials?: string | null
          id?: string
          is_access_to_software_granted?: boolean
          is_payroll_processing_needed: boolean
          payroll_frequency?: string | null
          step_3_id: string
          superannuation_fund?: string | null
          updated_at?: string
          workers_compensation_policy?: string | null
        }
        Update: {
          created_at?: string
          current_payroll_software?: string | null
          employee_count?: number | null
          encrypted_access_credentials?: string | null
          id?: string
          is_access_to_software_granted?: boolean
          is_payroll_processing_needed?: boolean
          payroll_frequency?: string | null
          step_3_id?: string
          superannuation_fund?: string | null
          updated_at?: string
          workers_compensation_policy?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payroll_step_3_id_fkey"
            columns: ["step_3_id"]
            isOneToOne: true
            referencedRelation: "step_3_financial_systems"
            referencedColumns: ["id"]
          },
        ]
      }
      permissions: {
        Row: {
          access_level: string
          consent_renewal_frequency: string | null
          created_at: string
          data_type: string
          granted_by_contact_id: string | null
          id: string
          permission_granted: boolean
          permission_granted_date: string | null
          permission_scope: string | null
          requires_ongoing_consent: boolean
          special_conditions: string | null
          step_5_id: string
          updated_at: string
        }
        Insert: {
          access_level: string
          consent_renewal_frequency?: string | null
          created_at?: string
          data_type: string
          granted_by_contact_id?: string | null
          id?: string
          permission_granted?: boolean
          permission_granted_date?: string | null
          permission_scope?: string | null
          requires_ongoing_consent?: boolean
          special_conditions?: string | null
          step_5_id: string
          updated_at?: string
        }
        Update: {
          access_level?: string
          consent_renewal_frequency?: string | null
          created_at?: string
          data_type?: string
          granted_by_contact_id?: string | null
          id?: string
          permission_granted?: boolean
          permission_granted_date?: string | null
          permission_scope?: string | null
          requires_ongoing_consent?: boolean
          special_conditions?: string | null
          step_5_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "permissions_granted_by_contact_id_fkey"
            columns: ["granted_by_contact_id"]
            isOneToOne: false
            referencedRelation: "contacts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "permissions_step_5_id_fkey"
            columns: ["step_5_id"]
            isOneToOne: false
            referencedRelation: "step_5_finalization"
            referencedColumns: ["id"]
          },
        ]
      }
      step_1_business_profile: {
        Row: {
          created_at: string
          id: string
          onboarding_session_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_session_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_session_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "step_1_business_profile_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: true
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      step_2_farm_operations: {
        Row: {
          created_at: string
          id: string
          onboarding_session_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_session_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_session_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "step_2_farm_operations_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: true
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      step_3_financial_systems: {
        Row: {
          created_at: string
          id: string
          onboarding_session_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_session_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_session_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "step_3_financial_systems_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: true
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      step_4_agreements: {
        Row: {
          created_at: string
          id: string
          onboarding_session_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_session_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_session_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "step_4_agreements_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: true
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      step_4_registrations_insurance: {
        Row: {
          created_at: string
          id: string
          onboarding_session_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_session_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_session_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "step_4_registrations_insurance_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: true
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      step_5_finalization: {
        Row: {
          created_at: string
          id: string
          onboarding_session_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          onboarding_session_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          onboarding_session_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "step_5_finalization_onboarding_session_id_fkey"
            columns: ["onboarding_session_id"]
            isOneToOne: true
            referencedRelation: "onboarding_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      suppliers: {
        Row: {
          address: string | null
          contact_details: string | null
          contact_person: string | null
          created_at: string
          email: string | null
          id: string
          phone: string | null
          services_provided: string
          step_2_id: string
          supplier_category: string | null
          supplier_name: string
          supplier_type: Database["farms"]["Enums"]["supplier_type_enum"] | null
          updated_at: string
        }
        Insert: {
          address?: string | null
          contact_details?: string | null
          contact_person?: string | null
          created_at?: string
          email?: string | null
          id?: string
          phone?: string | null
          services_provided: string
          step_2_id: string
          supplier_category?: string | null
          supplier_name: string
          supplier_type?:
            | Database["farms"]["Enums"]["supplier_type_enum"]
            | null
          updated_at?: string
        }
        Update: {
          address?: string | null
          contact_details?: string | null
          contact_person?: string | null
          created_at?: string
          email?: string | null
          id?: string
          phone?: string | null
          services_provided?: string
          step_2_id?: string
          supplier_category?: string | null
          supplier_name?: string
          supplier_type?:
            | Database["farms"]["Enums"]["supplier_type_enum"]
            | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "suppliers_step_2_id_fkey"
            columns: ["step_2_id"]
            isOneToOne: false
            referencedRelation: "step_2_farm_operations"
            referencedColumns: ["id"]
          },
        ]
      }
      vehicle_registrations: {
        Row: {
          annual_km_estimate: number | null
          created_at: string
          current_condition: string | null
          engine_number: string | null
          id: string
          insurance_expiry: string | null
          insurance_policy_number: string | null
          make: string
          model: string
          primary_use: string
          registration_expiry: string
          registration_number: string
          registration_state: string
          roadworthy_certificate_expiry: string | null
          step_4_id: string
          updated_at: string
          vehicle_type: Database["farms"]["Enums"]["vehicle_type_enum"]
          vin_chassis_number: string | null
          year: number | null
        }
        Insert: {
          annual_km_estimate?: number | null
          created_at?: string
          current_condition?: string | null
          engine_number?: string | null
          id?: string
          insurance_expiry?: string | null
          insurance_policy_number?: string | null
          make: string
          model: string
          primary_use: string
          registration_expiry: string
          registration_number: string
          registration_state: string
          roadworthy_certificate_expiry?: string | null
          step_4_id: string
          updated_at?: string
          vehicle_type: Database["farms"]["Enums"]["vehicle_type_enum"]
          vin_chassis_number?: string | null
          year?: number | null
        }
        Update: {
          annual_km_estimate?: number | null
          created_at?: string
          current_condition?: string | null
          engine_number?: string | null
          id?: string
          insurance_expiry?: string | null
          insurance_policy_number?: string | null
          make?: string
          model?: string
          primary_use?: string
          registration_expiry?: string
          registration_number?: string
          registration_state?: string
          roadworthy_certificate_expiry?: string | null
          step_4_id?: string
          updated_at?: string
          vehicle_type?: Database["farms"]["Enums"]["vehicle_type_enum"]
          vin_chassis_number?: string | null
          year?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "vehicle_registrations_step_4_id_fkey"
            columns: ["step_4_id"]
            isOneToOne: false
            referencedRelation: "step_4_registrations_insurance"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      accounting_method_enum: "Cash Accounting" | "Accrual Accounting"
      activity_type_enum:
        | "Cropping"
        | "Livestock"
        | "Mixed Farming"
        | "Horticulture"
        | "Dairy"
        | "Aquaculture"
        | "Forestry"
        | "Other"
      agreement_type_enum:
        | "Service Agreement"
        | "Privacy Policy"
        | "Direct Debit"
        | "Terms and Conditions"
      asset_category_enum: "Vehicle" | "Equipment" | "Insurance"
      authority_level_enum:
        | "Full Decision Making Authority"
        | "Operational Decisions Only"
        | "Information Contact Only"
      bas_frequency_enum: "Monthly" | "Quarterly" | "Annually"
      bas_preparation_enum:
        | "Self-Prepared"
        | "Accountant Prepared"
        | "BAS Agent"
        | "Bookkeeper"
      business_structure_enum:
        | "Sole Trader"
        | "Company"
        | "Partnership"
        | "Trust"
        | "Cooperative"
        | "Other"
      chart_of_accounts_setup_enum:
        | "Default Template"
        | "Custom Setup"
        | "Imported from Previous System"
        | "Accountant Configured"
      communication_method_enum: "Email" | "Phone" | "SMS" | "WhatsApp"
      contact_type_enum:
        | "Primary Contact"
        | "Owner/Director"
        | "General Manager"
        | "Operations Manager"
        | "Financial Controller"
        | "Accountant"
        | "BAS Agent"
        | "Key Staff"
        | "Admin"
        | "Accounts"
        | "Personal"
        | "Emergency Contact"
        | "Legal Representative"
        | "Insurance Contact"
        | "Supplier Contact"
        | "Other"
      employment_type_enum:
        | "Full-time"
        | "Part-time"
        | "Casual"
        | "Contract"
        | "Seasonal"
      insurance_type_enum:
        | "Public Liability"
        | "Product Liability"
        | "Professional Indemnity"
        | "Workers Compensation"
        | "Motor Vehicle"
        | "Property"
        | "Crop Insurance"
        | "Livestock Insurance"
        | "Business Interruption"
        | "Cyber Liability"
        | "Other"
      license_status_enum:
        | "Current"
        | "Pending Renewal"
        | "Expired"
        | "Under Review"
      license_type_enum:
        | "Chemical Permit"
        | "Machinery License"
        | "Food Safety Cert"
        | "Water License"
        | "Heavy Vehicle License"
        | "Quad Bike License"
        | "Other"
      renewal_frequency_enum:
        | "Annual"
        | "Biennial"
        | "Every 3 Years"
        | "Every 5 Years"
        | "One-time only"
      reporting_frequency_enum: "Weekly" | "Fortnightly" | "Monthly"
      supplier_type_enum:
        | "Chemical Supplier"
        | "Equipment Rental"
        | "Feed Supplier"
        | "Seed Supplier"
        | "Fertilizer Supplier"
        | "Veterinary Services"
        | "Transport Services"
        | "Maintenance Services"
        | "Other"
      vehicle_type_enum:
        | "Tractor"
        | "Harvester"
        | "Truck"
        | "Utility Vehicle"
        | "Trailer"
        | "Motorbike"
        | "Quad Bike"
        | "Other Farm Vehicle"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      profiles: {
        Row: {
          created_at: string | null
          full_name: string | null
          id: string
          phone_number: string | null
          receive_text_messages: boolean | null
          updated_at: string | null
          username: string | null
        }
        Insert: {
          created_at?: string | null
          full_name?: string | null
          id: string
          phone_number?: string | null
          receive_text_messages?: boolean | null
          updated_at?: string | null
          username?: string | null
        }
        Update: {
          created_at?: string | null
          full_name?: string | null
          id?: string
          phone_number?: string | null
          receive_text_messages?: boolean | null
          updated_at?: string | null
          username?: string | null
        }
        Relationships: []
      }
      receipts: {
        Row: {
          created_at: string
          error_log: string | null
          id: string
          original_filename: string | null
          sharepoint_web_url: string | null
          status: string
          supabase_storage_path: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          error_log?: string | null
          id?: string
          original_filename?: string | null
          sharepoint_web_url?: string | null
          status?: string
          supabase_storage_path: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          error_log?: string | null
          id?: string
          original_filename?: string | null
          sharepoint_web_url?: string | null
          status?: string
          supabase_storage_path?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      sharepoint_sync_map: {
        Row: {
          created_at: string | null
          created_by: string | null
          last_synced_at: string | null
          session_id: string | null
          sharepoint_drive_id: string
          sharepoint_etag: string | null
          sharepoint_file_id: string | null
          sharepoint_file_name: string
          sharepoint_item_id: string | null
          sharepoint_list_id: string | null
          sharepoint_metadata: Json | null
          sharepoint_relative_url: string
          sharepoint_site_id: string
          sharepoint_version: string | null
          sharepoint_web_url: string | null
          supabase_bucket_id: string
          supabase_file_path: string
          supabase_object_id: string
          sync_error_message: string | null
          sync_status: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          last_synced_at?: string | null
          session_id?: string | null
          sharepoint_drive_id: string
          sharepoint_etag?: string | null
          sharepoint_file_id?: string | null
          sharepoint_file_name: string
          sharepoint_item_id?: string | null
          sharepoint_list_id?: string | null
          sharepoint_metadata?: Json | null
          sharepoint_relative_url: string
          sharepoint_site_id: string
          sharepoint_version?: string | null
          sharepoint_web_url?: string | null
          supabase_bucket_id?: string
          supabase_file_path: string
          supabase_object_id: string
          sync_error_message?: string | null
          sync_status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          last_synced_at?: string | null
          session_id?: string | null
          sharepoint_drive_id?: string
          sharepoint_etag?: string | null
          sharepoint_file_id?: string | null
          sharepoint_file_name?: string
          sharepoint_item_id?: string | null
          sharepoint_list_id?: string | null
          sharepoint_metadata?: Json | null
          sharepoint_relative_url?: string
          sharepoint_site_id?: string
          sharepoint_version?: string | null
          sharepoint_web_url?: string | null
          supabase_bucket_id?: string
          supabase_file_path?: string
          supabase_object_id?: string
          sync_error_message?: string | null
          sync_status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      synced_files: {
        Row: {
          content_type: string | null
          created_at: string | null
          created_by: string | null
          error_details: Json | null
          error_message: string | null
          file_size: number | null
          id: string
          max_retries: number | null
          metadata: Json | null
          next_retry_at: string | null
          operation_type: string
          processed_at: string | null
          retry_count: number | null
          session_id: string | null
          status: string | null
          supabase_bucket_id: string
          supabase_file_path: string
          supabase_object_id: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          content_type?: string | null
          created_at?: string | null
          created_by?: string | null
          error_details?: Json | null
          error_message?: string | null
          file_size?: number | null
          id?: string
          max_retries?: number | null
          metadata?: Json | null
          next_retry_at?: string | null
          operation_type: string
          processed_at?: string | null
          retry_count?: number | null
          session_id?: string | null
          status?: string | null
          supabase_bucket_id?: string
          supabase_file_path: string
          supabase_object_id: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          content_type?: string | null
          created_at?: string | null
          created_by?: string | null
          error_details?: Json | null
          error_message?: string | null
          file_size?: number | null
          id?: string
          max_retries?: number | null
          metadata?: Json | null
          next_retry_at?: string | null
          operation_type?: string
          processed_at?: string | null
          retry_count?: number | null
          session_id?: string | null
          status?: string | null
          supabase_bucket_id?: string
          supabase_file_path?: string
          supabase_object_id?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      cleanup_old_sync_records: {
        Args: { older_than_days?: number }
        Returns: number
      }
      create_user_onboarding_session_with_steps: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      custom_access_token_hook: {
        Args: { event: Json }
        Returns: Json
      }
      delete_document: {
        Args: { p_document_id: string; p_session_id: string }
        Returns: string
      }
      finalize_document_upload: {
        Args: { payload: Json }
        Returns: string
      }
      get_onboarding_session_data: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_session_id_from_path: {
        Args: { p_path_name: string }
        Returns: string
      }
      update_encrypted_field: {
        Args: {
          p_table_name: string
          p_session_id: string
          p_field_name: string
          p_encrypted_value: string
        }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  farms: {
    Enums: {
      accounting_method_enum: ["Cash Accounting", "Accrual Accounting"],
      activity_type_enum: [
        "Cropping",
        "Livestock",
        "Mixed Farming",
        "Horticulture",
        "Dairy",
        "Aquaculture",
        "Forestry",
        "Other",
      ],
      agreement_type_enum: [
        "Service Agreement",
        "Privacy Policy",
        "Direct Debit",
        "Terms and Conditions",
      ],
      asset_category_enum: ["Vehicle", "Equipment", "Insurance"],
      authority_level_enum: [
        "Full Decision Making Authority",
        "Operational Decisions Only",
        "Information Contact Only",
      ],
      bas_frequency_enum: ["Monthly", "Quarterly", "Annually"],
      bas_preparation_enum: [
        "Self-Prepared",
        "Accountant Prepared",
        "BAS Agent",
        "Bookkeeper",
      ],
      business_structure_enum: [
        "Sole Trader",
        "Company",
        "Partnership",
        "Trust",
        "Cooperative",
        "Other",
      ],
      chart_of_accounts_setup_enum: [
        "Default Template",
        "Custom Setup",
        "Imported from Previous System",
        "Accountant Configured",
      ],
      communication_method_enum: ["Email", "Phone", "SMS", "WhatsApp"],
      contact_type_enum: [
        "Primary Contact",
        "Owner/Director",
        "General Manager",
        "Operations Manager",
        "Financial Controller",
        "Accountant",
        "BAS Agent",
        "Key Staff",
        "Admin",
        "Accounts",
        "Personal",
        "Emergency Contact",
        "Legal Representative",
        "Insurance Contact",
        "Supplier Contact",
        "Other",
      ],
      employment_type_enum: [
        "Full-time",
        "Part-time",
        "Casual",
        "Contract",
        "Seasonal",
      ],
      insurance_type_enum: [
        "Public Liability",
        "Product Liability",
        "Professional Indemnity",
        "Workers Compensation",
        "Motor Vehicle",
        "Property",
        "Crop Insurance",
        "Livestock Insurance",
        "Business Interruption",
        "Cyber Liability",
        "Other",
      ],
      license_status_enum: [
        "Current",
        "Pending Renewal",
        "Expired",
        "Under Review",
      ],
      license_type_enum: [
        "Chemical Permit",
        "Machinery License",
        "Food Safety Cert",
        "Water License",
        "Heavy Vehicle License",
        "Quad Bike License",
        "Other",
      ],
      renewal_frequency_enum: [
        "Annual",
        "Biennial",
        "Every 3 Years",
        "Every 5 Years",
        "One-time only",
      ],
      reporting_frequency_enum: ["Weekly", "Fortnightly", "Monthly"],
      supplier_type_enum: [
        "Chemical Supplier",
        "Equipment Rental",
        "Feed Supplier",
        "Seed Supplier",
        "Fertilizer Supplier",
        "Veterinary Services",
        "Transport Services",
        "Maintenance Services",
        "Other",
      ],
      vehicle_type_enum: [
        "Tractor",
        "Harvester",
        "Truck",
        "Utility Vehicle",
        "Trailer",
        "Motorbike",
        "Quad Bike",
        "Other Farm Vehicle",
      ],
    },
  },
  public: {
    Enums: {},
  },
} as const
