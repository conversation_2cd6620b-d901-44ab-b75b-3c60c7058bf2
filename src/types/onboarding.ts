// src/types/onboarding.ts

// This file contains the complete type definitions for the data returned
// by the `get_onboarding_session_data` PostgreSQL function. It mirrors the
// hierarchical structure of the V2 database schema.

import { Database } from './database.types'; // Ensure Database types are imported

// === DIRECT DATABASE TYPE IMPORTS ===
// Use the actual database types instead of redefining
type OnboardingSessionRow = Database['farms']['Tables']['onboarding_sessions']['Row'];
type BusinessRegistrationRow = Database['farms']['Tables']['business_registration']['Row'];
type ContactRow = Database['farms']['Tables']['contacts']['Row'];
type KeyStaffRow = Database['farms']['Tables']['key_staff']['Row'];
type AddressRow = Database['farms']['Tables']['addresses']['Row'];
type ActivityRow = Database['farms']['Tables']['activities']['Row'];
type LicenseRow = Database['farms']['Tables']['licenses']['Row'];
type SupplierRow = Database['farms']['Tables']['suppliers']['Row'];
type ContractRow = Database['farms']['Tables']['contracts']['Row'];
type ChemicalUsageRow = Database['farms']['Tables']['chemical_usage']['Row'];
type BookkeepingRow = Database['farms']['Tables']['bookkeeping']['Row'];
type PayrollRow = Database['farms']['Tables']['payroll']['Row'];
type AssetRow = Database['farms']['Tables']['assets']['Row'];
type DataMigrationRow = Database['farms']['Tables']['data_migration']['Row'];
type PermissionRow = Database['farms']['Tables']['permissions']['Row'];
type AgreementRow = Database['farms']['Tables']['agreements']['Row'];
type PaymentRow = Database['farms']['Tables']['payments']['Row'];
type CommunicationPreferencesRow = Database['farms']['Tables']['communication_preferences']['Row'];
type DocumentRow = Database['farms']['Tables']['documents']['Row'];

// === NEW STEP 4 & 5 DATABASE TYPE IMPORTS ===
type VehicleRegistrationRow = Database['farms']['Tables']['vehicle_registrations']['Row'];
type InsurancePolicyRow = Database['farms']['Tables']['insurance_policies']['Row'];
type LicenseNewRow = Database['farms']['Tables']['licenses_new']['Row'];
type FinalizationSubmissionRow = Database['farms']['Tables']['finalization_submission']['Row'];

// === ENUM TYPE EXPORTS ===
export type ActivityTypeEnum = Database['farms']['Enums']['activity_type_enum'];
export type ContactTypeEnum = Database['farms']['Enums']['contact_type_enum'];
export type BusinessStructureEnum = Database['farms']['Enums']['business_structure_enum'];
export type LicenseTypeEnum = Database['farms']['Enums']['license_type_enum'];
export type AssetCategoryEnum = Database['farms']['Enums']['asset_category_enum'];
export type CommunicationMethodEnum = Database['farms']['Enums']['communication_method_enum'];
export type ReportingFrequencyEnum = Database['farms']['Enums']['reporting_frequency_enum'];
export type AgreementTypeEnum = Database['farms']['Enums']['agreement_type_enum'];
export type BasFrequencyEnum = Database['farms']['Enums']['bas_frequency_enum'];

// === NEW STEP 4 & 5 ENUM TYPE EXPORTS ===
export type VehicleTypeEnum = Database['farms']['Enums']['vehicle_type_enum'];
export type InsuranceTypeEnum = Database['farms']['Enums']['insurance_type_enum'];
export type LicenseStatusEnum = Database['farms']['Enums']['license_status_enum'];
export type RenewalFrequencyEnum = Database['farms']['Enums']['renewal_frequency_enum'];

// === ENHANCED TYPES WITH DATABASE COMPATIBILITY ===
// Export the database types directly for maximum compatibility
export type OnboardingSession = OnboardingSessionRow;
export type BusinessRegistration = BusinessRegistrationRow;
export type Contact = ContactRow;
export type KeyStaff = KeyStaffRow;
export type Address = AddressRow;
export type Activity = ActivityRow;
export type License = LicenseRow;
export type Supplier = SupplierRow;
export type Contract = ContractRow;
export type ChemicalUsage = ChemicalUsageRow;
export type Bookkeeping = BookkeepingRow;
export type Payroll = PayrollRow;
export type Asset = AssetRow;
export type DataMigration = DataMigrationRow;
export type Permission = PermissionRow;
export type Agreement = AgreementRow;
export type Payment = PaymentRow;
export type CommunicationPreferences = CommunicationPreferencesRow;
export type Document = DocumentRow;

// === NEW STEP 4 & 5 ENHANCED TYPES ===
export type VehicleRegistration = VehicleRegistrationRow;
export type InsurancePolicy = InsurancePolicyRow;
export type LicenseNew = LicenseNewRow;
export type FinalizationSubmission = FinalizationSubmissionRow;

// === STEP CONTAINER TYPES ===
// These represent the hierarchical structure returned by get_onboarding_session_data()
export interface Step1BusinessProfileWithChildren {
    id: string;
    onboarding_session_id: string;
    created_at: string;
    updated_at: string;
    businessRegistration: BusinessRegistration | null;
    addresses: Address[];
    contacts: Contact[];
    keyStaff: KeyStaff[];
}

export interface Step2FarmOperationsWithChildren {
    id: string;
    onboarding_session_id: string;
    created_at: string;
    updated_at: string;
    activities: Activity[];
    licenses: License[];
    suppliers: Supplier[];
    contracts: Contract[];
    chemicalUsage: ChemicalUsage[];
}

export interface Step3FinancialSystemsWithChildren {
    id: string;
    onboarding_session_id: string;
    created_at: string;
    updated_at: string;
    bookkeeping: Bookkeeping | null;
    payroll: Payroll | null;
    assets: Asset[];
}

// === NEW STEP 4 & 5 CONTAINER TYPES ===
export interface Step4RegistrationsInsuranceWithChildren {
    id: string;
    onboarding_session_id: string;
    created_at: string;
    updated_at: string;
    vehicleRegistrations: VehicleRegistration[];
    insurancePolicies: InsurancePolicy[];
    licensesNew: LicenseNew[];
}

export interface Step5FinalizationWithChildren {
    id: string;
    onboarding_session_id: string;
    created_at: string;
    updated_at: string;
    dataMigration: DataMigration | null;
    permissions: Permission[];
    agreements: Agreement[];
    payments: Payment | null;
    communicationPreferences: CommunicationPreferences | null;
    finalizationSubmission: FinalizationSubmission | null;
}

// === DEPRECATED STEP 4 (LEGACY SUPPORT) ===
// This interface is kept for backward compatibility during migration
export interface Step4AgreementsWithChildren {
    id: string;
    onboarding_session_id: string;
    created_at: string;
    updated_at: string;
    dataMigration: DataMigration | null;
    permissions: Permission[];
    agreements: Agreement[];
    payments: Payment | null;
    communicationPreferences: CommunicationPreferences | null;
}

// === COMPLETE SESSION DATA STRUCTURE ===
export interface FullOnboardingSessionData {
    onboardingSession: OnboardingSession | null;
    step1_businessProfile: Step1BusinessProfileWithChildren | null;
    step2_farmOperations: Step2FarmOperationsWithChildren | null;
    step3_financialSystems: Step3FinancialSystemsWithChildren | null;
    step4_registrationsInsurance: Step4RegistrationsInsuranceWithChildren | null;
    step5_finalization: Step5FinalizationWithChildren | null;
    documents: Document[];

    // === DEPRECATED FIELDS (LEGACY SUPPORT) ===
    // These fields are kept for backward compatibility during migration
    step4_agreements?: Step4AgreementsWithChildren | null;
}

// === VALIDATION HELPER TYPES ===
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    fieldErrors: Record<string, string[]>;
}

export interface StepValidationSummary {
    step1: ValidationResult;
    step2: ValidationResult;
    step3: ValidationResult;
    step4: ValidationResult;
    step5: ValidationResult;
    overall: ValidationResult;
}

// === FORM STATE TYPES ===
export type FormMode = 'create' | 'edit' | 'view';
export type StepNumber = 1 | 2 | 3 | 4 | 5;
export type SessionStatus = 'in_progress' | 'completed' | 'archived';

// === UTILITY TYPES ===
export type RequiredFields<T> = {
    [K in keyof T]-?: T[K];
};

export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;

// === ARRAY FIELD TYPES ===
export type ArrayFieldTypes = {
    crop_varieties: string[] | null;
    document_categories: string[] | null;
    preferred_methods: CommunicationMethodEnum[];
    special_conditions: string[] | null; // For insurance policies
    validation_errors: string[] | null; // For finalization submission
    validation_warnings: string[] | null; // For finalization submission
};

// === VALIDATION FUNCTIONS ===
export const validateEnumValue = <T extends keyof Database['farms']['Enums']>(
    enumType: T,
    value: string
): value is Database['farms']['Enums'][T] => {
    // This would need to be implemented with actual enum values
    // For now, return true as validation happens server-side
    return true;
};

export const validateArrayField = <K extends keyof ArrayFieldTypes>(
    fieldName: K,
    value: ArrayFieldTypes[K]
): boolean => {
    if (fieldName === 'preferred_methods') {
        return Array.isArray(value) && value.length > 0;
    }
    return Array.isArray(value);
};
