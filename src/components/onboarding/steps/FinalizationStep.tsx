import React, { useState } from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useEnhancedStepInit } from '@/hooks/use-enhanced-step-init';
import { useEntityListManagement } from '@/hooks/use-entity-list-management';
import { StepLoadingState, StepErrorState } from '@/components/ui/step-status';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { BusinessInfoSection } from './review/BusinessInfoSection';
import { FarmOpsReviewSection } from './review/FarmOpsReviewSection';
import { FinancialsReviewSection } from './review/FinancialsReviewSection';
import { TermsAgreementSection } from './review/TermsAgreementSection';
import { PaymentInfoSection } from './review/PaymentInfoSection';
import { DigitalSignatureSection } from './review/DigitalSignatureSection';
import { DataMigrationSection } from './review/DataMigrationSection';
import { CommunicationPreferencesSection } from './review/CommunicationPreferencesSection';
import { SubmitButton } from './review/SubmitButton';
import { useToast } from '@/hooks/use-toast';
import { logger } from '@/utils/logger';
import { Database } from '@/types/database.types';
import { 
  validateCompleteOnboarding, 
  groupErrorsByStep, 
  formatValidationErrors 
} from '@/utils/agreements-validation';

// Database type aliases for entity management
type FarmsSchema = Database['farms'];
type AgreementInsert = FarmsSchema['Tables']['agreements']['Insert'];
type AgreementUpdate = FarmsSchema['Tables']['agreements']['Update'];

const SERVICE_AGREEMENT_TYPE = "Service Agreement";

/**
 * FinalizationStep Component - Step 5 of Onboarding
 * 
 * Handles the final step of the onboarding process including:
 * - Review of all collected information
 * - Terms and agreements acceptance
 * - Payment information setup
 * - Digital signature capture
 * - Data migration preferences
 * - Communication preferences
 * - Final submission
 * 
 * Refactored to use consolidated hook system following established patterns
 * for consistency and stability across the onboarding wizard.
 */
export const FinalizationStep = () => {
    const {
        sessionData,
        loading: contextLoading,
        sessionId,
        ensureStep5FinalizationRecordExists,
        ensureDataMigrationRecordExists,
        ensurePermissionsRecordExists,
        ensurePaymentsRecordExists,
        ensureCommunicationPreferencesRecordExists,
        helpers: { getStepId }
    } = useOnboarding();
    const { toast } = useToast();

    // Enhanced step initialization for Step 5
    const {
        isStepInitializing,
        initializationError,
        handleRetryInitialization,
        isLoading,
        effectiveStepId,
    } = useEnhancedStepInit({
        stepName: 'Finalization',
        sessionId,
        contextLoading,
        existingStepId: getStepId(sessionData, 'step5'),
        ensureStepFunction: ensureStep5FinalizationRecordExists,
    });

    // Base disabled state for all operations
    const baseDisabled = !effectiveStepId || contextLoading || isStepInitializing;

    // Agreements Management using consolidated hooks
    const agreementsManager = useEntityListManagement<
        FarmsSchema['Tables']['agreements']['Row'],
        AgreementInsert,
        AgreementUpdate
    >({
        stepId: effectiveStepId,
        tableName: 'agreements',
        initialEntities: sessionData?.step5_finalization?.agreements || [],
        createDefaultEntity: (stepId) => ({
            step_5_id: stepId,
            agreement_type: SERVICE_AGREEMENT_TYPE as Database['farms']['Enums']['agreement_type_enum'],
            is_agreed: false,
        }),
        entityDisplayName: 'agreement',
        disabled: baseDisabled,
        maxEntities: 10,
        minEntities: 1,
    });

    // Validation state
    const [validationErrors, setValidationErrors] = useState<string[]>([]);
    const [isValidating, setIsValidating] = useState(false);

    // Handle comprehensive validation
    const handleValidateOnboarding = async () => {
        if (!sessionData) return;

        setIsValidating(true);
        setValidationErrors([]);

        try {
            const validationResult = validateCompleteOnboarding(sessionData);
            
            if (!validationResult.isValid) {
                const groupedErrors = groupErrorsByStep(validationResult.errors);
                const formattedErrors = formatValidationErrors(groupedErrors);
                setValidationErrors(formattedErrors);
                
                toast({
                    title: "Validation Issues Found",
                    description: `Please address ${validationResult.errors.length} validation issues before proceeding.`,
                    variant: "destructive",
                });
            } else {
                toast({
                    title: "Validation Successful",
                    description: "All required information has been provided correctly.",
                    variant: "default",
                });
            }
        } catch (error) {
            logger.error('Validation error:', error);
            toast({
                title: "Validation Error",
                description: "An error occurred during validation. Please try again.",
                variant: "destructive",
            });
        } finally {
            setIsValidating(false);
        }
    };

    // Loading state
    if (isLoading && !initializationError) {
        return <StepLoadingState stepName="Finalization" message="Preparing final review and submission..." />;
    }

    // Error state
    if (initializationError) {
        return (
            <StepErrorState
                stepName="Finalization"
                error={initializationError}
                onRetry={handleRetryInitialization}
                isRetrying={isStepInitializing}
            />
        );
    }

    return (
        <div className="space-y-8">
            <div>
                <h2 className="text-2xl font-bold">Step 5: Review & Finalization</h2>
                <p className="text-muted-foreground">
                    Review your information, complete agreements, and finalize your onboarding to NewTerra's agricultural business administration services.
                </p>
            </div>

            {/* Validation Summary */}
            {validationErrors.length > 0 && (
                <Card className="border-destructive">
                    <CardHeader>
                        <CardTitle className="text-destructive">Validation Issues</CardTitle>
                        <CardDescription>
                            Please address the following issues before finalizing your onboarding:
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ul className="list-disc list-inside space-y-1">
                            {validationErrors.map((error, index) => (
                                <li key={index} className="text-sm text-destructive">{error}</li>
                            ))}
                        </ul>
                    </CardContent>
                </Card>
            )}

            {/* Review Sections */}
            <div className="space-y-6">
                <BusinessInfoSection sessionData={sessionData} />
                <FarmOpsReviewSection sessionData={sessionData} />
                <FinancialsReviewSection sessionData={sessionData} />
                
                {/* Step 4 Review - New registrations and insurance */}
                <Card>
                    <CardHeader>
                        <CardTitle>Registrations & Insurance</CardTitle>
                        <CardDescription>Vehicle registrations, insurance policies, and licenses</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span className="font-medium">Vehicle Registrations:</span>
                                <span className="ml-2">{sessionData?.step4_registrationsInsurance?.vehicleRegistrations?.length || 0}</span>
                            </div>
                            <div>
                                <span className="font-medium">Insurance Policies:</span>
                                <span className="ml-2">{sessionData?.step4_registrationsInsurance?.insurancePolicies?.length || 0}</span>
                            </div>
                            <div>
                                <span className="font-medium">Licenses & Permits:</span>
                                <span className="ml-2">{sessionData?.step4_registrationsInsurance?.licensesNew?.length || 0}</span>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Finalization Sections */}
            <div className="space-y-6">
                <TermsAgreementSection 
                    stepId={effectiveStepId}
                    agreements={agreementsManager.entities}
                    onUpdateAgreement={agreementsManager.updateEntity}
                    disabled={baseDisabled}
                />
                
                <PaymentInfoSection 
                    stepId={effectiveStepId}
                    disabled={baseDisabled}
                />
                
                <DataMigrationSection 
                    stepId={effectiveStepId}
                    disabled={baseDisabled}
                />
                
                <CommunicationPreferencesSection 
                    stepId={effectiveStepId}
                    disabled={baseDisabled}
                />
                
                <DigitalSignatureSection 
                    stepId={effectiveStepId}
                    disabled={baseDisabled}
                />
            </div>

            {/* Validation and Submission */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t">
                <Button
                    onClick={handleValidateOnboarding}
                    disabled={baseDisabled || isValidating}
                    variant="outline"
                    className="flex-1"
                >
                    {isValidating ? 'Validating...' : 'Validate Information'}
                </Button>
                
                <SubmitButton 
                    stepId={effectiveStepId}
                    disabled={baseDisabled || validationErrors.length > 0}
                    sessionData={sessionData}
                />
            </div>
        </div>
    );
};
