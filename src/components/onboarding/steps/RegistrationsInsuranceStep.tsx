import React, { useState } from 'react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useEnhancedStepInit } from '@/hooks/use-enhanced-step-init';
import { useEntityListManagement } from '@/hooks/use-entity-list-management';
import { StepLoadingState, StepErrorState } from '@/components/ui/step-status';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FormSection } from '@/components/form_components/FormSection';
import { VehicleRegistrationForm } from '@/components/form_components/VehicleRegistrationForm';
import { InsurancePolicyForm } from '@/components/form_components/InsurancePolicyForm';
import { LicenseForm } from '@/components/form_components/LicenseForm';
import { Button } from '@/components/ui/button';
import { Plus, Car, Shield, FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { logger } from '@/utils/logger';
import type { Database } from '@/types/database.types';
import type { TableInsert, TableUpdate } from '@/contexts/OnboardingContext';

// Database type aliases for entity management
type FarmsSchema = Database['farms'];
type VehicleRegistrationInsert = FarmsSchema['Tables']['vehicle_registrations']['Insert'];
type VehicleRegistrationUpdate = FarmsSchema['Tables']['vehicle_registrations']['Update'];
type VehicleRegistrationRow = FarmsSchema['Tables']['vehicle_registrations']['Row'];

type InsurancePolicyInsert = FarmsSchema['Tables']['insurance_policies']['Insert'];
type InsurancePolicyUpdate = FarmsSchema['Tables']['insurance_policies']['Update'];
type InsurancePolicyRow = FarmsSchema['Tables']['insurance_policies']['Row'];

type LicenseNewInsert = FarmsSchema['Tables']['licenses_new']['Insert'];
type LicenseNewUpdate = FarmsSchema['Tables']['licenses_new']['Update'];
type LicenseNewRow = FarmsSchema['Tables']['licenses_new']['Row'];

/**
 * RegistrationsInsuranceStep Component - Step 4 of Onboarding
 * 
 * Manages vehicle registrations, insurance policies, and enhanced licenses for the farm business.
 * Uses consolidated hooks for form management and entity list management with tabbed interface.
 * 
 * Architecture:
 * 1. Initialize step using useEnhancedStepInit
 * 2. Set up entity list management for all three entity types
 * 3. Provide tabbed interface for clear separation of functionality
 * 4. Implement proper validation and error handling for each section
 */
export const RegistrationsInsuranceStep: React.FC = () => {
  const {
    sessionData,
    loading: contextLoading,
    sessionId,
    ensureStep4RegistrationsInsuranceRecordExists,
  } = useOnboarding();
  const { toast } = useToast();

  // Enhanced step initialization
  const {
    isStepInitializing,
    initializationError,
    handleRetryInitialization,
    isLoading,
    effectiveStepId,
  } = useEnhancedStepInit({
    stepName: 'RegistrationsInsurance',
    sessionId,
    contextLoading,
    existingStepId: sessionData?.step4_registrationsInsurance?.id,
    ensureStepFunction: ensureStep4RegistrationsInsuranceRecordExists,
  });

  // Base disabled state for all operations
  const baseDisabled = !effectiveStepId || contextLoading || isStepInitializing;

  // Vehicle Registrations Management
  const vehicleRegistrationsManager = useEntityListManagement<
    VehicleRegistrationRow,
    VehicleRegistrationInsert,
    VehicleRegistrationUpdate
  >({
    stepId: effectiveStepId,
    tableName: 'vehicle_registrations',
    initialEntities: sessionData?.step4_registrationsInsurance?.vehicleRegistrations || [],
    createDefaultEntity: (stepId) => ({
      step_4_id: stepId,
      vehicle_type: 'Utility Vehicle' as Database['farms']['Enums']['vehicle_type_enum'],
      make: '',
      model: '',
      registration_number: '',
      registration_expiry: '',
      registration_state: 'NSW',
      primary_use: '',
    }),
    entityDisplayName: 'vehicle registration',
    disabled: baseDisabled,
    maxEntities: 20,
    minEntities: 0,
  });

  // Insurance Policies Management
  const insurancePoliciesManager = useEntityListManagement<
    InsurancePolicyRow,
    InsurancePolicyInsert,
    InsurancePolicyUpdate
  >({
    stepId: effectiveStepId,
    tableName: 'insurance_policies',
    initialEntities: sessionData?.step4_registrationsInsurance?.insurancePolicies || [],
    createDefaultEntity: (stepId) => ({
      step_4_id: stepId,
      insurance_type: 'Public Liability' as Database['farms']['Enums']['insurance_type_enum'],
      policy_number: '',
      insurer_name: '',
      policy_start_date: '',
      policy_expiry_date: '',
    }),
    entityDisplayName: 'insurance policy',
    disabled: baseDisabled,
    maxEntities: 15,
    minEntities: 0,
  });

  // Enhanced Licenses Management (migrated from Step 2)
  const licensesManager = useEntityListManagement<
    LicenseNewRow,
    LicenseNewInsert,
    LicenseNewUpdate
  >({
    stepId: effectiveStepId,
    tableName: 'licenses_new',
    initialEntities: sessionData?.step4_registrationsInsurance?.licensesNew || [],
    createDefaultEntity: (stepId) => ({
      step_4_id: stepId,
      license_type: 'Chemical Permit' as Database['farms']['Enums']['license_type_enum'],
      license_status: 'Current' as Database['farms']['Enums']['license_status_enum'],
    }),
    entityDisplayName: 'license',
    disabled: baseDisabled,
    maxEntities: 10,
    minEntities: 0,
  });

  // Tab state management
  const [activeTab, setActiveTab] = useState('vehicles');

  // Loading state
  if (isLoading && !initializationError) {
    return <StepLoadingState stepName="Registrations & Insurance" message="Loading registrations & insurance essentials..." />;
  }

  // Error state
  if (initializationError) {
    return (
      <StepErrorState
        stepName="Registrations & Insurance"
        error={initializationError}
        onRetry={handleRetryInitialization}
        isRetrying={isStepInitializing}
      />
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold">Step 4: Registrations & Insurance</h2>
        <p className="text-muted-foreground">
          Manage your farm's vehicle registrations, insurance policies, and licenses for comprehensive compliance tracking.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="vehicles" className="flex items-center gap-2">
            <Car className="h-4 w-4" />
            Vehicle Registrations
          </TabsTrigger>
          <TabsTrigger value="insurance" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Insurance Policies
          </TabsTrigger>
          <TabsTrigger value="licenses" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Licenses & Permits
          </TabsTrigger>
        </TabsList>

        {/* Vehicle Registrations Tab */}
        <TabsContent value="vehicles" className="space-y-6">
          <FormSection
            title="Vehicle Registrations"
            description="Track all farm vehicles, their registration details, and compliance requirements."
            headerActions={
              <Button
                onClick={vehicleRegistrationsManager.addEntity}
                disabled={!vehicleRegistrationsManager.canAddEntity || baseDisabled}
                size="sm"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Vehicle
              </Button>
            }
          >
            {vehicleRegistrationsManager.entities.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Car className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No vehicles registered yet.</p>
                <p className="text-sm">Add your first vehicle to get started.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {vehicleRegistrationsManager.entities.map((vehicle) => (
                  <div key={vehicle.id} className="border rounded-lg p-6">
                    <VehicleRegistrationForm
                      entity={vehicle}
                      disabled={baseDisabled}
                      onUpdate={vehicleRegistrationsManager.updateEntity}
                      onDelete={vehicleRegistrationsManager.deleteEntity}
                      isDeleting={vehicleRegistrationsManager.isDeletingEntity === vehicle.id}
                    />
                  </div>
                ))}
              </div>
            )}
          </FormSection>
        </TabsContent>

        {/* Insurance Policies Tab */}
        <TabsContent value="insurance" className="space-y-6">
          <FormSection
            title="Insurance Policies"
            description="Manage all insurance policies covering your farm operations, equipment, and liability."
            headerActions={
              <Button
                onClick={insurancePoliciesManager.addEntity}
                disabled={!insurancePoliciesManager.canAddEntity || baseDisabled}
                size="sm"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Policy
              </Button>
            }
          >
            {insurancePoliciesManager.entities.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No insurance policies added yet.</p>
                <p className="text-sm">Add your first policy to ensure proper coverage tracking.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {insurancePoliciesManager.entities.map((policy) => (
                  <div key={policy.id} className="border rounded-lg p-6">
                    <InsurancePolicyForm
                      entity={policy}
                      disabled={baseDisabled}
                      onUpdate={insurancePoliciesManager.updateEntity}
                      onDelete={insurancePoliciesManager.deleteEntity}
                      isDeleting={insurancePoliciesManager.isDeletingEntity === policy.id}
                    />
                  </div>
                ))}
              </div>
            )}
          </FormSection>
        </TabsContent>

        {/* Enhanced Licenses Tab */}
        <TabsContent value="licenses" className="space-y-6">
          <FormSection
            title="Licenses & Permits"
            description="Track all licenses, permits, and certifications required for your farm operations."
            headerActions={
              <Button
                onClick={licensesManager.addEntity}
                disabled={!licensesManager.canAddEntity || baseDisabled}
                size="sm"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add License
              </Button>
            }
          >
            {licensesManager.entities.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No licenses or permits added yet.</p>
                <p className="text-sm">Add your first license to track compliance requirements.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {licensesManager.entities.map((license) => (
                  <div key={license.id} className="border rounded-lg p-6">
                    <LicenseForm
                      entity={license}
                      disabled={baseDisabled}
                      onUpdate={licensesManager.updateEntity}
                      onDelete={licensesManager.deleteEntity}
                      isDeleting={licensesManager.isDeletingEntity === license.id}
                    />
                  </div>
                ))}
              </div>
            )}
          </FormSection>
        </TabsContent>
      </Tabs>
    </div>
  );
};
