import React, { useState, useCallback } from 'react';
import { AutoSaveFormField } from './FormField';
import { FileUploadField } from './FileUploadField';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { LICENSE_TYPES, EntityFormComponentProps } from '@/types/form-components';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/types/database.types';

// Enhanced license form data interface for licenses_new table
export interface LicenseNewFormData {
  id: string;
  step_4_id: string;
  license_type: Database['farms']['Enums']['license_type_enum'];
  license_number: string | null;
  issuing_authority: string | null;
  issue_date: string | null;
  expiry_date: string | null;
  license_status: Database['farms']['Enums']['license_status_enum'];
  renewal_frequency: Database['farms']['Enums']['renewal_frequency_enum'] | null;
  renewal_cost: number | null;
  last_renewal_date: string | null;
  next_renewal_reminder_date: string | null;
  compliance_notes: string | null;
  created_at: string;
  updated_at: string;
}

// License status options
const LICENSE_STATUS_OPTIONS = [
  { value: 'Current', label: 'Current' },
  { value: 'Pending Renewal', label: 'Pending Renewal' },
  { value: 'Expired', label: 'Expired' },
  { value: 'Under Review', label: 'Under Review' },
];

// Renewal frequency options
const RENEWAL_FREQUENCY_OPTIONS = [
  { value: 'Annual', label: 'Annual' },
  { value: 'Biennial', label: 'Biennial' },
  { value: 'Every 3 Years', label: 'Every 3 Years' },
  { value: 'Every 5 Years', label: 'Every 5 Years' },
  { value: 'One-time only', label: 'One-time only' },
];

export const LicenseForm: React.FC<EntityFormComponentProps<LicenseNewFormData>> = ({
  entity,
  disabled,
  onUpdate,
  onDelete,
  isDeleting = false,
}) => {
  const { toast } = useToast();
  const { uploadAndFinalizeDocument } = useOnboarding();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');

  // Create wrapper for updateFn to match expected signature
  const updateWrapper = async (tableName: string, id: string, data: Record<string, unknown>) => {
    await onUpdate(id, data as Partial<LicenseNewFormData>);
  };

  // Handle file upload for license documents
  const handleFileUpload = useCallback(async (file: File) => {
    if (!entity.id) return;

    setIsUploading(true);
    setUploadError('');

    try {
      const result = await uploadAndFinalizeDocument(
        file,
        'licenses_new', // relatedToEntity
        entity.id   // relatedToId
      );

      if (result.success) {
        toast({
          title: "Success",
          description: "License document uploaded successfully",
          variant: "default",
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadError(errorMessage);

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [entity.id, uploadAndFinalizeDocument, toast]);

  // Handle file removal
  const handleFileRemove = useCallback(async () => {
    // Implementation would depend on your document management system
    toast({
      title: "Document Removed",
      description: "License document has been removed",
      variant: "default",
    });
  }, [toast]);

  // Handle delete with confirmation
  const handleDelete = useCallback(async () => {
    if (window.confirm('Are you sure you want to delete this license? This action cannot be undone.')) {
      await onDelete?.(entity.id);
    }
  }, [entity.id, onDelete]);

  return (
    <div className="space-y-6">
      {/* Header with delete button */}
      <div className="flex justify-between items-start">
        <div>
          <h4 className="text-lg font-medium">
            {entity.license_type && entity.license_number
              ? `${entity.license_type} - ${entity.license_number}`
              : entity.license_type || 'License'}
          </h4>
          <p className="text-sm text-muted-foreground">
            Status: {entity.license_status || 'Not specified'}
          </p>
        </div>
        {onDelete && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleDelete}
            disabled={disabled || isDeleting}
            className="text-destructive hover:text-destructive"
          >
            {isDeleting ? (
              <>Deleting...</>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </>
            )}
          </Button>
        )}
      </div>

      {/* Basic License Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="License Type"
          name="license_type"
          type="select"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.license_type}
          options={LICENSE_TYPES}
          required
          validationRules={[
            { required: true, message: 'License type is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="License Status"
          name="license_status"
          type="select"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.license_status}
          options={LICENSE_STATUS_OPTIONS}
          required
          validationRules={[
            { required: true, message: 'License status is required' }
          ]}
          disabled={disabled}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="License Number"
          name="license_number"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.license_number}
          placeholder="e.g., CHM-12345"
          maxLength={100}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Issuing Authority"
          name="issuing_authority"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.issuing_authority}
          placeholder="e.g., NSW Department of Primary Industries"
          maxLength={200}
          disabled={disabled}
        />
      </div>

      {/* License Dates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Issue Date"
          name="issue_date"
          type="date"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.issue_date}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Expiry Date"
          name="expiry_date"
          type="date"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.expiry_date}
          validationRules={[
            {
              custom: (value: string) => {
                if (!value || !entity.issue_date) return null;
                const issueDate = new Date(entity.issue_date);
                const expiryDate = new Date(value);
                if (expiryDate <= issueDate) {
                  return 'Expiry date must be after issue date';
                }
                return null;
              },
              message: 'Invalid expiry date'
            }
          ]}
          disabled={disabled}
        />
      </div>

      {/* Renewal Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <AutoSaveFormField
          label="Renewal Frequency"
          name="renewal_frequency"
          type="select"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.renewal_frequency}
          options={RENEWAL_FREQUENCY_OPTIONS}
          placeholder="Select frequency"
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Renewal Cost"
          name="renewal_cost"
          type="number"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.renewal_cost}
          placeholder="e.g., 150.00"
          min={0}
          step={0.01}
          validationRules={[
            {
              custom: (value: string | number | null) => {
                if (!value) return null;
                const cost = typeof value === 'string' ? parseFloat(value) : value;
                if (cost < 0) {
                  return 'Renewal cost must be positive';
                }
                return null;
              },
              message: 'Invalid renewal cost'
            }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Last Renewal Date"
          name="last_renewal_date"
          type="date"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.last_renewal_date}
          disabled={disabled}
        />
      </div>

      {/* Reminder and Compliance */}
      <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
        <AutoSaveFormField
          label="Next Renewal Reminder Date"
          name="next_renewal_reminder_date"
          type="date"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.next_renewal_reminder_date}
          validationRules={[
            {
              custom: (value: string) => {
                if (!value) return null;
                const reminderDate = new Date(value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                if (reminderDate < today) {
                  return 'Reminder date should be in the future';
                }
                return null;
              },
              message: 'Invalid reminder date'
            }
          ]}
          disabled={disabled}
        />
      </div>

      {/* Compliance Notes */}
      <div className="grid grid-cols-1 gap-4">
        <AutoSaveFormField
          label="Compliance Notes"
          name="compliance_notes"
          type="textarea"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.compliance_notes}
          placeholder="Add any compliance requirements, conditions, or notes..."
          maxLength={1000}
          disabled={disabled}
        />
      </div>

      {/* Renewal Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <AutoSaveFormField
          label="Renewal Frequency"
          name="renewal_frequency"
          type="select"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.renewal_frequency}
          options={RENEWAL_FREQUENCY_OPTIONS}
          placeholder="Select frequency"
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Renewal Cost"
          name="renewal_cost"
          type="number"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.renewal_cost}
          placeholder="e.g., 150.00"
          min={0}
          step={0.01}
          validationRules={[
            {
              custom: (value: string | number | null) => {
                if (!value) return null;
                const cost = typeof value === 'string' ? parseFloat(value) : value;
                if (cost < 0) {
                  return 'Renewal cost must be positive';
                }
                return null;
              },
              message: 'Invalid renewal cost'
            }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Last Renewal Date"
          name="last_renewal_date"
          type="date"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.last_renewal_date}
          disabled={disabled}
        />
      </div>

      {/* Reminder and Compliance */}
      <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
        <AutoSaveFormField
          label="Next Renewal Reminder Date"
          name="next_renewal_reminder_date"
          type="date"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.next_renewal_reminder_date}
          validationRules={[
            {
              custom: (value: string) => {
                if (!value) return null;
                const reminderDate = new Date(value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                if (reminderDate < today) {
                  return 'Reminder date should be in the future';
                }
                return null;
              },
              message: 'Invalid reminder date'
            }
          ]}
          disabled={disabled}
        />
      </div>

      {/* Compliance Notes */}
      <div className="grid grid-cols-1 gap-4">
        <AutoSaveFormField
          label="Compliance Notes"
          name="compliance_notes"
          type="textarea"
          entityId={entity.id}
          tableName="licenses_new"
          updateFn={updateWrapper}
          value={entity.compliance_notes}
          placeholder="Add any compliance requirements, conditions, or notes..."
          maxLength={1000}
          disabled={disabled}
        />
      </div>

      {/* File Upload Section */}
      <div className="pt-4 border-t">
        <FileUploadField
          label="License Documents"
          name="license_documents"
          onUpload={handleFileUpload}
          disabled={disabled || isUploading}
          accept=".pdf,.jpg,.jpeg,.png"
          validation={{
            allowedTypes: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
            maxSize: 10 * 1024 * 1024, // 10MB
          }}
          isUploading={isUploading}
          error={uploadError}
          description="Upload license certificates, permits, or compliance documents (PDF, JPG, PNG - max 10MB)"
          placeholder="Click or drag to upload license documents"
          onRemoveFile={handleFileRemove}
        />
      </div>
    </div>
  );
};