import React, { useState, useCallback } from 'react';
import { AutoSaveFormField } from './FormField';
import { FileUploadField } from './FileUploadField';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';
import { EntityFormComponentProps } from '@/types/form-components';
import { Database } from '@/types/database.types';

// Insurance policy form data interface
export interface InsurancePolicyFormData {
  id: string;
  step_4_id: string;
  insurance_type: Database['farms']['Enums']['insurance_type_enum'];
  policy_number: string;
  insurer_name: string;
  coverage_amount: number | null;
  excess_amount: number | null;
  coverage_description: string | null;
  policy_start_date: string;
  policy_expiry_date: string;
  annual_premium: number | null;
  payment_frequency: string | null;
  broker_contact: string | null;
  claims_contact: string | null;
  policy_conditions: string | null;
  risk_category: string | null;
  special_conditions: string[] | null;
  created_at: string;
  updated_at: string;
}

// Insurance type options based on database enum
const INSURANCE_TYPES = [
  { value: 'Public Liability', label: 'Public Liability' },
  { value: 'Product Liability', label: 'Product Liability' },
  { value: 'Professional Indemnity', label: 'Professional Indemnity' },
  { value: 'Workers Compensation', label: 'Workers Compensation' },
  { value: 'Motor Vehicle', label: 'Motor Vehicle' },
  { value: 'Property', label: 'Property' },
  { value: 'Crop Insurance', label: 'Crop Insurance' },
  { value: 'Livestock Insurance', label: 'Livestock Insurance' },
  { value: 'Business Interruption', label: 'Business Interruption' },
  { value: 'Cyber Liability', label: 'Cyber Liability' },
  { value: 'Other', label: 'Other' },
];

// Payment frequency options
const PAYMENT_FREQUENCIES = [
  { value: 'Monthly', label: 'Monthly' },
  { value: 'Quarterly', label: 'Quarterly' },
  { value: 'Semi-annually', label: 'Semi-annually' },
  { value: 'Annually', label: 'Annually' },
  { value: 'One-time', label: 'One-time' },
];

// Risk category options
const RISK_CATEGORIES = [
  { value: 'Low', label: 'Low Risk' },
  { value: 'Medium', label: 'Medium Risk' },
  { value: 'High', label: 'High Risk' },
  { value: 'Critical', label: 'Critical Risk' },
];

export const InsurancePolicyForm: React.FC<EntityFormComponentProps<InsurancePolicyFormData>> = ({
  entity,
  disabled,
  onUpdate,
  onDelete,
  isDeleting = false,
}) => {
  const { toast } = useToast();
  const { uploadAndFinalizeDocument } = useOnboarding();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');

  // Create wrapper for updateFn to match expected signature
  const updateWrapper = async (tableName: string, id: string, data: Record<string, unknown>) => {
    await onUpdate(id, data as Partial<InsurancePolicyFormData>);
  };

  // Handle file upload for insurance documents
  const handleFileUpload = useCallback(async (file: File) => {
    if (!entity.id) return;

    setIsUploading(true);
    setUploadError('');

    try {
      const result = await uploadAndFinalizeDocument(
        file,
        'insurance_policies', // relatedToEntity
        entity.id   // relatedToId
      );

      if (result.success) {
        toast({
          title: "Success",
          description: "Insurance document uploaded successfully",
          variant: "default",
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadError(errorMessage);

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [entity.id, uploadAndFinalizeDocument, toast]);

  // Handle file removal
  const handleFileRemove = useCallback(async () => {
    toast({
      title: "Document Removed",
      description: "Insurance document has been removed",
      variant: "default",
    });
  }, [toast]);

  // Handle delete with confirmation
  const handleDelete = useCallback(async () => {
    if (window.confirm('Are you sure you want to delete this insurance policy? This action cannot be undone.')) {
      await onDelete(entity.id);
    }
  }, [entity.id, onDelete]);

  return (
    <div className="space-y-6">
      {/* Header with delete button */}
      <div className="flex justify-between items-start">
        <div>
          <h4 className="text-lg font-medium">
            {entity.insurance_type && entity.insurer_name
              ? `${entity.insurance_type} - ${entity.insurer_name}`
              : 'Insurance Policy'}
          </h4>
          <p className="text-sm text-muted-foreground">
            Policy: {entity.policy_number || 'Not specified'}
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleDelete}
          disabled={disabled || isDeleting}
          className="text-destructive hover:text-destructive"
        >
          {isDeleting ? (
            <>Deleting...</>
          ) : (
            <>
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </>
          )}
        </Button>
      </div>

      {/* Basic Policy Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Insurance Type"
          name="insurance_type"
          type="select"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.insurance_type}
          options={INSURANCE_TYPES}
          required
          validationRules={[
            { required: true, message: 'Insurance type is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Policy Number"
          name="policy_number"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.policy_number}
          placeholder="e.g., POL123456789"
          required
          validationRules={[
            { required: true, message: 'Policy number is required' }
          ]}
          maxLength={100}
          disabled={disabled}
        />
      </div>

      {/* Insurer Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Insurer Name"
          name="insurer_name"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.insurer_name}
          placeholder="e.g., NRMA Insurance"
          required
          validationRules={[
            { required: true, message: 'Insurer name is required' }
          ]}
          maxLength={200}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Broker Contact"
          name="broker_contact"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.broker_contact}
          placeholder="e.g., John Smith - 0412 345 678"
          maxLength={200}
          disabled={disabled}
        />
      </div>

      {/* Policy Dates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Policy Start Date"
          name="policy_start_date"
          type="date"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.policy_start_date}
          required
          validationRules={[
            { required: true, message: 'Policy start date is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Policy Expiry Date"
          name="policy_expiry_date"
          type="date"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.policy_expiry_date}
          required
          validationRules={[
            { required: true, message: 'Policy expiry date is required' },
            {
              custom: (value: string) => {
                if (!value || !entity.policy_start_date) return null;
                const startDate = new Date(entity.policy_start_date);
                const expiryDate = new Date(value);
                if (expiryDate <= startDate) {
                  return 'Expiry date must be after start date';
                }
                return null;
              },
              message: 'Invalid expiry date'
            }
          ]}
          disabled={disabled}
        />
      </div>

      {/* Coverage and Financial Details */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <AutoSaveFormField
          label="Coverage Amount"
          name="coverage_amount"
          type="number"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.coverage_amount}
          placeholder="e.g., 1000000"
          min={0}
          step={1000}
          validationRules={[
            {
              custom: (value: string | number | null) => {
                if (!value) return null;
                const amount = typeof value === 'string' ? parseFloat(value) : value;
                if (amount < 0) {
                  return 'Coverage amount must be positive';
                }
                return null;
              },
              message: 'Invalid coverage amount'
            }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Excess Amount"
          name="excess_amount"
          type="number"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.excess_amount}
          placeholder="e.g., 500"
          min={0}
          step={100}
          validationRules={[
            {
              custom: (value: string | number | null) => {
                if (!value) return null;
                const amount = typeof value === 'string' ? parseFloat(value) : value;
                if (amount < 0) {
                  return 'Excess amount must be positive';
                }
                return null;
              },
              message: 'Invalid excess amount'
            }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Annual Premium"
          name="annual_premium"
          type="number"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.annual_premium}
          placeholder="e.g., 2500"
          min={0}
          step={100}
          validationRules={[
            {
              custom: (value: string | number | null) => {
                if (!value) return null;
                const amount = typeof value === 'string' ? parseFloat(value) : value;
                if (amount < 0) {
                  return 'Annual premium must be positive';
                }
                return null;
              },
              message: 'Invalid premium amount'
            }
          ]}
          disabled={disabled}
        />
      </div>

      {/* Payment and Risk Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Payment Frequency"
          name="payment_frequency"
          type="select"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.payment_frequency}
          options={PAYMENT_FREQUENCIES}
          placeholder="Select payment frequency"
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Risk Category"
          name="risk_category"
          type="select"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.risk_category}
          options={RISK_CATEGORIES}
          placeholder="Select risk category"
          disabled={disabled}
        />
      </div>

      {/* Contact Information */}
      <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
        <AutoSaveFormField
          label="Claims Contact"
          name="claims_contact"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.claims_contact}
          placeholder="e.g., Claims Department - 1800 123 456"
          maxLength={200}
          disabled={disabled}
        />
      </div>

      {/* Coverage Description */}
      <div className="grid grid-cols-1 gap-4">
        <AutoSaveFormField
          label="Coverage Description"
          name="coverage_description"
          type="textarea"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.coverage_description}
          placeholder="Describe what is covered by this policy..."
          maxLength={1000}
          disabled={disabled}
        />
      </div>

      {/* Policy Conditions */}
      <div className="grid grid-cols-1 gap-4">
        <AutoSaveFormField
          label="Policy Conditions"
          name="policy_conditions"
          type="textarea"
          entityId={entity.id}
          tableName="insurance_policies"
          updateFn={updateWrapper}
          value={entity.policy_conditions}
          placeholder="List any specific policy conditions or requirements..."
          maxLength={1000}
          disabled={disabled}
        />
      </div>

      {/* File Upload Section */}
      <div className="pt-4 border-t">
        <FileUploadField
          label="Insurance Documents"
          name="insurance_documents"
          onUpload={handleFileUpload}
          disabled={disabled || isUploading}
          accept=".pdf,.jpg,.jpeg,.png"
          validation={{
            allowedTypes: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
            maxSize: 10 * 1024 * 1024, // 10MB
          }}
          isUploading={isUploading}
          error={uploadError}
          description="Upload policy documents, certificates, or correspondence (PDF, JPG, PNG - max 10MB)"
          placeholder="Click or drag to upload insurance documents"
          onRemoveFile={handleFileRemove}
        />
      </div>
    </div>
  );
};
