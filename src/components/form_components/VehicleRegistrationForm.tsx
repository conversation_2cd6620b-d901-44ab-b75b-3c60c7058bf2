import React, { useState, useCallback } from 'react';
import { AutoSaveFormField } from './FormField';
import { FileUploadField } from './FileUploadField';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { useOnboarding } from '@/contexts/OnboardingContext';
import { useToast } from '@/hooks/use-toast';
import { EntityFormComponentProps } from '@/types/form-components';
import { Database } from '@/types/database.types';

// Vehicle registration form data interface
export interface VehicleRegistrationFormData {
  id: string;
  step_4_id: string;
  vehicle_type: Database['farms']['Enums']['vehicle_type_enum'];
  make: string;
  model: string;
  year: number | null;
  registration_number: string;
  registration_expiry: string;
  registration_state: string;
  vin_chassis_number: string | null;
  engine_number: string | null;
  insurance_policy_number: string | null;
  insurance_expiry: string | null;
  roadworthy_certificate_expiry: string | null;
  primary_use: string;
  current_condition: string | null;
  annual_km_estimate: number | null;
  created_at: string;
  updated_at: string;
}

// Vehicle type options based on database enum
const VEHICLE_TYPES = [
  { value: 'Tractor', label: 'Tractor' },
  { value: 'Harvester', label: 'Harvester' },
  { value: 'Truck', label: 'Truck' },
  { value: 'Utility Vehicle', label: 'Utility Vehicle/Ute' },
  { value: 'Trailer', label: 'Trailer' },
  { value: 'Motorbike', label: 'Motorbike' },
  { value: 'Quad Bike', label: 'Quad Bike' },
  { value: 'Other Farm Vehicle', label: 'Other Farm Vehicle' },
];

// Australian states for registration
const AUSTRALIAN_STATES = [
  { value: 'NSW', label: 'New South Wales' },
  { value: 'VIC', label: 'Victoria' },
  { value: 'QLD', label: 'Queensland' },
  { value: 'WA', label: 'Western Australia' },
  { value: 'SA', label: 'South Australia' },
  { value: 'TAS', label: 'Tasmania' },
  { value: 'ACT', label: 'Australian Capital Territory' },
  { value: 'NT', label: 'Northern Territory' },
];

// Primary use options
const PRIMARY_USE_OPTIONS = [
  { value: 'Farm Operations', label: 'Farm Operations' },
  { value: 'Transport', label: 'Transport' },
  { value: 'Personal Use', label: 'Personal Use' },
  { value: 'Commercial', label: 'Commercial' },
  { value: 'Other', label: 'Other' },
];

// Vehicle condition options
const CONDITION_OPTIONS = [
  { value: 'Excellent', label: 'Excellent' },
  { value: 'Good', label: 'Good' },
  { value: 'Fair', label: 'Fair' },
  { value: 'Poor', label: 'Poor' },
  { value: 'Requires Repair', label: 'Requires Repair' },
];

export const VehicleRegistrationForm: React.FC<EntityFormComponentProps<VehicleRegistrationFormData>> = ({
  entity,
  disabled,
  onUpdate,
  onDelete,
  isDeleting = false,
}) => {
  const { toast } = useToast();
  const { uploadAndFinalizeDocument } = useOnboarding();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string>('');

  // Create wrapper for updateFn to match expected signature
  const updateWrapper = async (tableName: string, id: string, data: Record<string, unknown>) => {
    await onUpdate(id, data as Partial<VehicleRegistrationFormData>);
  };

  // Handle file upload for vehicle documents
  const handleFileUpload = useCallback(async (file: File) => {
    if (!entity.id) return;

    setIsUploading(true);
    setUploadError('');

    try {
      const result = await uploadAndFinalizeDocument(
        file,
        'vehicle_registrations', // relatedToEntity
        entity.id   // relatedToId
      );

      if (result.success) {
        toast({
          title: "Success",
          description: "Vehicle document uploaded successfully",
          variant: "default",
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadError(errorMessage);

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [entity.id, uploadAndFinalizeDocument, toast]);

  // Handle file removal
  const handleFileRemove = useCallback(async () => {
    toast({
      title: "Document Removed",
      description: "Vehicle document has been removed",
      variant: "default",
    });
  }, [toast]);

  // Handle delete with confirmation
  const handleDelete = useCallback(async () => {
    if (window.confirm('Are you sure you want to delete this vehicle registration? This action cannot be undone.')) {
      await onDelete(entity.id);
    }
  }, [entity.id, onDelete]);

  return (
    <div className="space-y-6">
      {/* Header with delete button */}
      <div className="flex justify-between items-start">
        <div>
          <h4 className="text-lg font-medium">
            {entity.make && entity.model ? `${entity.make} ${entity.model}` : 'Vehicle Registration'}
          </h4>
          <p className="text-sm text-muted-foreground">
            Registration: {entity.registration_number || 'Not specified'}
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleDelete}
          disabled={disabled || isDeleting}
          className="text-destructive hover:text-destructive"
        >
          {isDeleting ? (
            <>Deleting...</>
          ) : (
            <>
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </>
          )}
        </Button>
      </div>

      {/* Vehicle Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Vehicle Type"
          name="vehicle_type"
          type="select"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.vehicle_type}
          options={VEHICLE_TYPES}
          required
          validationRules={[
            { required: true, message: 'Vehicle type is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Primary Use"
          name="primary_use"
          type="select"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.primary_use}
          options={PRIMARY_USE_OPTIONS}
          required
          validationRules={[
            { required: true, message: 'Primary use is required' }
          ]}
          disabled={disabled}
        />
      </div>

      {/* Make, Model, Year */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <AutoSaveFormField
          label="Make"
          name="make"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.make}
          placeholder="e.g., John Deere, Ford"
          required
          validationRules={[
            { required: true, message: 'Make is required' }
          ]}
          maxLength={100}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Model"
          name="model"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.model}
          placeholder="e.g., 6120R, Ranger"
          required
          validationRules={[
            { required: true, message: 'Model is required' }
          ]}
          maxLength={100}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Year"
          name="year"
          type="number"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.year}
          placeholder="e.g., 2020"
          min={1900}
          max={new Date().getFullYear() + 1}
          validationRules={[
            {
              custom: (value: string | number | null) => {
                if (!value) return null;
                const year = typeof value === 'string' ? parseInt(value) : value;
                const currentYear = new Date().getFullYear();
                if (year < 1900 || year > currentYear + 1) {
                  return `Year must be between 1900 and ${currentYear + 1}`;
                }
                return null;
              },
              message: 'Invalid year'
            }
          ]}
          disabled={disabled}
        />
      </div>

      {/* Registration Details */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <AutoSaveFormField
          label="Registration Number"
          name="registration_number"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.registration_number}
          placeholder="e.g., ABC123"
          required
          validationRules={[
            { required: true, message: 'Registration number is required' }
          ]}
          maxLength={20}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Registration State"
          name="registration_state"
          type="select"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.registration_state}
          options={AUSTRALIAN_STATES}
          required
          validationRules={[
            { required: true, message: 'Registration state is required' }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Registration Expiry"
          name="registration_expiry"
          type="date"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.registration_expiry}
          required
          validationRules={[
            { required: true, message: 'Registration expiry is required' },
            {
              custom: (value: string) => {
                if (!value) return null;
                const expiryDate = new Date(value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                if (expiryDate <= today) {
                  return 'Registration expiry must be in the future';
                }
                return null;
              },
              message: 'Invalid expiry date'
            }
          ]}
          disabled={disabled}
        />
      </div>

      {/* Vehicle Identification */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="VIN/Chassis Number"
          name="vin_chassis_number"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.vin_chassis_number}
          placeholder="e.g., 1HGBH41JXMN109186"
          maxLength={50}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Engine Number"
          name="engine_number"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.engine_number}
          placeholder="e.g., ENG123456"
          maxLength={50}
          disabled={disabled}
        />
      </div>

      {/* Insurance Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AutoSaveFormField
          label="Insurance Policy Number"
          name="insurance_policy_number"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.insurance_policy_number}
          placeholder="e.g., POL123456789"
          maxLength={50}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Insurance Expiry"
          name="insurance_expiry"
          type="date"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.insurance_expiry}
          validationRules={[
            {
              custom: (value: string) => {
                if (!value) return null;
                const expiryDate = new Date(value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                if (expiryDate <= today) {
                  return 'Insurance expiry should be in the future';
                }
                return null;
              },
              message: 'Invalid insurance expiry date'
            }
          ]}
          disabled={disabled}
        />
      </div>

      {/* Additional Details */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <AutoSaveFormField
          label="Current Condition"
          name="current_condition"
          type="select"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.current_condition}
          options={CONDITION_OPTIONS}
          placeholder="Select condition"
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Annual KM Estimate"
          name="annual_km_estimate"
          type="number"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.annual_km_estimate}
          placeholder="e.g., 15000"
          min={0}
          validationRules={[
            {
              custom: (value: string | number | null) => {
                if (!value) return null;
                const km = typeof value === 'string' ? parseInt(value) : value;
                if (km < 0) {
                  return 'Annual KM estimate must be positive';
                }
                return null;
              },
              message: 'Invalid KM estimate'
            }
          ]}
          disabled={disabled}
        />

        <AutoSaveFormField
          label="Roadworthy Certificate Expiry"
          name="roadworthy_certificate_expiry"
          type="date"
          entityId={entity.id}
          tableName="vehicle_registrations"
          updateFn={updateWrapper}
          value={entity.roadworthy_certificate_expiry}
          disabled={disabled}
        />
      </div>

      {/* File Upload Section */}
      <div className="pt-4 border-t">
        <FileUploadField
          label="Vehicle Documents"
          name="vehicle_documents"
          onUpload={handleFileUpload}
          disabled={disabled || isUploading}
          accept=".pdf,.jpg,.jpeg,.png"
          validation={{
            allowedTypes: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
            maxSize: 10 * 1024 * 1024, // 10MB
          }}
          isUploading={isUploading}
          error={uploadError}
          description="Upload registration papers, insurance documents, or roadworthy certificates (PDF, JPG, PNG - max 10MB)"
          placeholder="Click or drag to upload vehicle documents"
          onRemoveFile={handleFileRemove}
        />
      </div>
    </div>
  );
};
