/**
 * OnboardingContext Helper Utilities
 * 
 * This file contains utility functions that work with the OnboardingContext
 * to provide common operations and reduce code duplication across components.
 */

import { Database } from '@/types/database.types';
import {
    FullOnboardingSessionData,
    Step1BusinessProfileWithChildren,
    Step2FarmOperationsWithChildren,
    Step3FinancialSystemsWithChildren,
    Step4RegistrationsInsuranceWithChildren,
    Step5FinalizationWithChildren,
    Step4AgreementsWithChildren,
} from '@/types/onboarding';

type FarmsSchema = Database['farms'];
type TableName = keyof FarmsSchema['Tables'];
type TableRow<T extends TableName> = FarmsSchema['Tables'][T]['Row'];

// --- Data Access Helpers ---

/**
 * Safely extracts a step container from session data
 */
export const getStepData = <T extends 'step1' | 'step2' | 'step3' | 'step4' | 'step5'>(
    sessionData: FullOnboardingSessionData | null,
    step: T
): T extends 'step1'
    ? Step1BusinessProfileWithChildren | null
    : T extends 'step2'
    ? Step2FarmOperationsWithChildren | null
    : T extends 'step3'
    ? Step3FinancialSystemsWithChildren | null
    : T extends 'step4'
    ? Step4RegistrationsInsuranceWithChildren | null
    : T extends 'step5'
    ? Step5FinalizationWithChildren | null
    : never => {
    if (!sessionData) {
        return null as T extends 'step1'
            ? Step1BusinessProfileWithChildren | null
            : T extends 'step2'
            ? Step2FarmOperationsWithChildren | null
            : T extends 'step3'
            ? Step3FinancialSystemsWithChildren | null
            : T extends 'step4'
            ? Step4RegistrationsInsuranceWithChildren | null
            : T extends 'step5'
            ? Step5FinalizationWithChildren | null
            : never;
    }

    switch (step) {
        case 'step1':
            return sessionData.step1_businessProfile as T extends 'step1'
                ? Step1BusinessProfileWithChildren | null
                : never;
        case 'step2':
            return sessionData.step2_farmOperations as T extends 'step2'
                ? Step2FarmOperationsWithChildren | null
                : never;
        case 'step3':
            return sessionData.step3_financialSystems as T extends 'step3'
                ? Step3FinancialSystemsWithChildren | null
                : never;
        case 'step4':
            return sessionData.step4_registrationsInsurance as T extends 'step4'
                ? Step4RegistrationsInsuranceWithChildren | null
                : never;
        case 'step5':
            return sessionData.step5_finalization as T extends 'step5'
                ? Step5FinalizationWithChildren | null
                : never;
        default:
            return null as never;
    }
};

/**
 * Gets the ID for a specific step container
 */
export const getStepId = (
    sessionData: FullOnboardingSessionData | null,
    step: 'step1' | 'step2' | 'step3' | 'step4' | 'step5'
): string | null => {
    const stepData = getStepData(sessionData, step);
    return stepData?.id || null;
};

/**
 * Checks if a step has been initialized (has a database record)
 */
export const isStepInitialized = (
    sessionData: FullOnboardingSessionData | null,
    step: 'step1' | 'step2' | 'step3' | 'step4' | 'step5'
): boolean => {
    return !!getStepId(sessionData, step);
};

/**
 * Gets all child records of a specific type from a step
 */
export const getStepChildRecords = <T extends keyof {
    step1: Step1BusinessProfileWithChildren;
    step2: Step2FarmOperationsWithChildren;
    step3: Step3FinancialSystemsWithChildren;
    step4: Step4RegistrationsInsuranceWithChildren;
    step5: Step5FinalizationWithChildren;
}>(
    sessionData: FullOnboardingSessionData | null,
    step: T,
    childType: T extends 'step1'
        ? 'businessRegistration' | 'addresses' | 'contacts' | 'keyStaff'
        : T extends 'step2'
        ? 'activities' | 'licenses' | 'suppliers' | 'contracts' | 'chemicalUsage'
        : T extends 'step3'
        ? 'bookkeeping' | 'payroll' | 'assets'
        : T extends 'step4'
        ? 'vehicleRegistrations' | 'insurancePolicies' | 'licensesNew'
        : T extends 'step5'
        ? 'dataMigration' | 'permissions' | 'agreements' | 'payments' | 'communicationPreferences' | 'finalizationSubmission'
        : never
) => {
    const stepData = getStepData(sessionData, step);
    if (!stepData) return null;

    return (stepData as Record<string, unknown>)[childType as string] || null;
};

// --- Validation Helpers ---

/**
 * Checks if session data is available and valid
 */
export const isSessionDataValid = (sessionData: FullOnboardingSessionData | null): boolean => {
    return !!(sessionData?.onboardingSession?.id);
};

/**
 * Gets the current onboarding session ID
 */
export const getSessionId = (sessionData: FullOnboardingSessionData | null): string | null => {
    return sessionData?.onboardingSession?.id || null;
};

/**
 * Gets the current step number from session data
 */
export const getCurrentStep = (sessionData: FullOnboardingSessionData | null): number => {
    return sessionData?.onboardingSession?.current_step || 1;
};

/**
 * Gets the session status
 */
export const getSessionStatus = (sessionData: FullOnboardingSessionData | null): string => {
    return sessionData?.onboardingSession?.status || 'in_progress';
};

// --- Record State Helpers ---

/**
 * Checks if a specific record exists in the session data
 */
export const hasRecord = (
    sessionData: FullOnboardingSessionData | null,
    step: 'step1' | 'step2' | 'step3' | 'step4' | 'step5',
    recordType: string
): boolean => {
    const stepData = getStepData(sessionData, step);
    if (!stepData) return false;

    const record = (stepData as Record<string, unknown>)[recordType];
    return Array.isArray(record) ? record.length > 0 : !!record;
};

/**
 * Counts records of a specific type in a step
 */
export const countRecords = (
    sessionData: FullOnboardingSessionData | null,
    step: 'step1' | 'step2' | 'step3' | 'step4' | 'step5',
    recordType: string
): number => {
    const stepData = getStepData(sessionData, step);
    if (!stepData) return 0;

    const records = (stepData as Record<string, unknown>)[recordType];
    return Array.isArray(records) ? records.length : (records ? 1 : 0);
};

// --- Utility Functions ---

/**
 * Converts a table name to a human-readable form
 */
export const humanizeTableName = (tableName: string): string => {
    return tableName
        .replace(/_/g, ' ')
        .replace(/\b\w/g, char => char.toUpperCase())
        .replace(/Step \d /, ''); // Remove step prefixes
};

/**
 * Creates a default error handler for OnboardingContext operations
 */
export const createErrorHandler = (operation: string, context?: Record<string, unknown>) => {
    return (error: unknown) => {
        console.error(`[OnboardingContext] ${operation} failed:`, {
            error: error instanceof Error ? error.message : String(error),
            context,
            timestamp: new Date().toISOString(),
        });

        return {
            success: false,
            error: error instanceof Error ? error.message : 'An unknown error occurred',
        };
    };
};

/**
 * Type guard to check if an error is a PostgreSQL error
 */
export const isPostgresError = (error: unknown): error is {
    code: string;
    message: string;
    details?: string;
    hint?: string;
} => {
    return (
        error !== null &&
        typeof error === 'object' &&
        'code' in error &&
        'message' in error &&
        typeof (error as Record<string, unknown>).code === 'string' &&
        typeof (error as Record<string, unknown>).message === 'string'
    );
};

/**
 * Formats a PostgreSQL error for user display
 */
export const formatPostgresError = (error: unknown): string => {
    if (isPostgresError(error)) {
        // Handle common PostgreSQL error codes
        switch (error.code) {
            case '23505': // Unique constraint violation
                return 'This record already exists. Please check your data and try again.';
            case '23503': // Foreign key constraint violation
                return 'This operation would create an invalid reference. Please ensure all required data is present.';
            case '23502': // Not null constraint violation
                return 'Required information is missing. Please fill in all required fields.';
            case '42P01': // Undefined table
                return 'Database configuration error. Please contact support.';
            default:
                return error.message || 'A database error occurred.';
        }
    }

    if (error instanceof Error) {
        return error.message;
    }

    return 'An unknown error occurred.';
};

// --- Constants ---

/**
 * Mapping of step numbers to their corresponding data keys
 */
export const STEP_DATA_KEYS = {
    1: 'step1_businessProfile' as const,
    2: 'step2_farmOperations' as const,
    3: 'step3_financialSystems' as const,
    4: 'step4_registrationsInsurance' as const,
    5: 'step5_finalization' as const,
} as const;

/**
 * Mapping of steps to their ensure function names
 */
export const STEP_ENSURE_FUNCTIONS = {
    1: 'ensureStep1BusinessProfileRecordExists' as const,
    2: 'ensureStep2FarmOperationsRecordExists' as const,
    3: 'ensureStep3FinancialSystemsRecordExists' as const,
    4: 'ensureStep4RegistrationsInsuranceRecordExists' as const,
    5: 'ensureStep5FinalizationRecordExists' as const,
} as const;

/**
 * DEPRECATED: Legacy Step 4 Agreements mapping (Backward Compatibility)
 */
export const LEGACY_STEP_DATA_KEYS = {
    4: 'step4_agreements' as const,
} as const;

export const LEGACY_STEP_ENSURE_FUNCTIONS = {
    4: 'ensureStep4AgreementsRecordExists' as const,
} as const;